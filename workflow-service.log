    )
    ^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/grpc/_common.py", line 156, in wait
    _wait_once(wait_fn, MAXIMUM_WAIT_TIMEOUT, spin_cb)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/workflow-service-i6PuvLFB-py3.13/lib/python3.13/site-packages/grpc/_common.py", line 116, in _wait_once
    wait_fn(timeout=timeout)
    ~~~~~~~^^^^^^^^^^^^^^^^^
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py", line 659, in wait
    signaled = self._cond.wait(timeout)
  File "/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py", line 363, in wait
    gotit = waiter.acquire(True, timeout)
KeyboardInterrupt
Pratham-ka-MacBook-Air:workflow-service prathamagarwal$ 
Pratham-ka-MacBook-Air:workflow-service prathamagarwal$ 
Pratham-ka-MacBook-Air:workflow-service prathamagarwal$ clear
Pratham-ka-MacBook-Air:workflow-service prathamagarwal$ ./run_local.sh
Installing dependencies...
Installing dependencies from lock file

No dependencies to install or update
Generating gRPC code...
Successfully generated gRPC code
Successfully fixed imports in generated files
Initializing database...
Starting Workflow Service...
DEBUG: StartNode class loaded
DEBUG: BaseAgentComponent class loaded with is_abstract = True
DEBUG: AI component AgenticAI loaded
DEBUG: BasicLLMChain class loaded
DEBUG: ApiRequestNode class loaded
2025-07-04 17:35:23 - workflow-service - INFO - Loading all components...
2025-07-04 17:35:23 - component-loader - INFO - Loading all components...
2025-07-04 17:35:23 - component-loader - INFO - Base component path: /Users/<USER>/Desktop/ruh_ai/backend/workflow-service/app/components
2025-07-04 17:35:23 - component-loader - INFO - Imported main component packages
Warning: MCP config file not found at /Users/<USER>/Desktop/ruh_ai/backend/workflow-service/mcp_server_configs.json
2025-07-04 17:35:23 - component-loader - INFO - Found class MCPToolsComponent in module app.components.tools.mcp_tools
2025-07-04 17:35:23 - component-loader - INFO - Found class BaseNode in module app.components.core.base_node
2025-07-04 17:35:23 - component-loader - INFO - Found class SaveToFileComponent in module app.components.processing.save_to_file
2025-07-04 17:35:23 - component-loader - INFO - Found class UniversalConverterComponent in module app.components.processing.universal_converter
2025-07-04 17:35:23 - component-loader - INFO - Found class CombineTextComponent in module app.components.processing.combine_text
2025-07-04 17:35:23 - component-loader - INFO - Found class AlterMetadataComponent in module app.components.processing.alter_metadata
2025-07-04 17:35:23 - component-loader - INFO - Found class DelayComponent in module app.components.processing.delay_time
2025-07-04 17:35:23 - component-loader - INFO - Found class SplitTextComponent in module app.components.processing.split_text
2025-07-04 17:35:23 - component-loader - INFO - Found class MergeDataComponent in module app.components.processing.merge_data
2025-07-04 17:35:23 - component-loader - INFO - Found class SelectDataComponent in module app.components.processing.select_data
2025-07-04 17:35:23 - component-loader - INFO - Found class DataToDataFrameComponent in module app.components.processing.data_to_dataframe
2025-07-04 17:35:23 - component-loader - INFO - Found class MessageToDataComponent in module app.components.processing.message_to_data
2025-07-04 17:35:23 - component-loader - INFO - Found class StartNode in module app.components.io.start_node
2025-07-04 17:35:23 - component-loader - INFO - Found class SlackComponent in module app.components.hitl.slack_component
2025-07-04 17:35:23 - component-loader - INFO - Found class GmailComponent in module app.components.hitl.gmail_component
2025-07-04 17:35:23 - component-loader - INFO - Found class TelegramComponent in module app.components.hitl.telegram_component
2025-07-04 17:35:23 - component-loader - INFO - Found class EmailComponent in module app.components.hitl.email_component
2025-07-04 17:35:23 - component-loader - INFO - Found class BaseHITLComponent in module app.components.hitl.base_hitl_component
2025-07-04 17:35:23 - component-loader - INFO - Found class DiscordComponent in module app.components.hitl.discord_component
2025-07-04 17:35:23 - component-loader - INFO - Found class HITLOrchestrator in module app.components.hitl.hitl_orchestrator
2025-07-04 17:35:23 - component-loader - INFO - Found class WhatsAppComponent in module app.components.hitl.whatsapp_component
2025-07-04 17:35:23 - component-loader - INFO - Found class BaseDataInteractionComponent in module app.components.data_interaction.base_data_interaction_component
2025-07-04 17:35:23 - component-loader - INFO - Found class WebhookComponent in module app.components.data_interaction.webhook
2025-07-04 17:35:23 - component-loader - INFO - Found class ApiRequestNode in module app.components.data_interaction.api_request
2025-07-04 17:35:23 - component-loader - INFO - Found class BasicLLMChain in module app.components.ai.basic_llm_chain
2025-07-04 17:35:23 - component-loader - INFO - Found class Classifier in module app.components.ai.classifier
2025-07-04 17:35:23 - component-loader - INFO - Found class AgenticAI in module app.components.ai.agentic_ai
2025-07-04 17:35:23 - component-loader - INFO - Found class BaseAgentComponent in module app.components.ai.base_agent_component
2025-07-04 17:35:23 - component-loader - INFO - Found class QuestionAnswerModule in module app.components.ai.question_answer_module
2025-07-04 17:35:23 - component-loader - INFO - Found class StartOutboundCallComponent in module app.components.ai.outbound_caller
2025-07-04 17:35:23 - component-loader - INFO - Found class Summarizer in module app.components.ai.summarizer
2025-07-04 17:35:23 - component-loader - INFO - Found class InformationExtractor in module app.components.ai.information_extractor
2025-07-04 17:35:23 - component-loader - INFO - Found class SentimentAnalyzer in module app.components.ai.sentiment_analyzer
2025-07-04 17:35:23 - component-loader - INFO - Found class ErrorHandling in module app.components.control_flow.loopNode
2025-07-04 17:35:23 - component-loader - INFO - Found class ExitCondition in module app.components.control_flow.loopNode
2025-07-04 17:35:23 - component-loader - INFO - Found class IterationSettings in module app.components.control_flow.loopNode
2025-07-04 17:35:23 - component-loader - INFO - Found class IterationSource in module app.components.control_flow.loopNode
2025-07-04 17:35:23 - component-loader - INFO - Found class LoopConfig in module app.components.control_flow.loopNode
2025-07-04 17:35:23 - component-loader - INFO - Found class LoopNode in module app.components.control_flow.loopNode
2025-07-04 17:35:23 - component-loader - INFO - Found class RangeConfig in module app.components.control_flow.loopNode
2025-07-04 17:35:23 - component-loader - INFO - Found class ResultAggregation in module app.components.control_flow.loopNode
2025-07-04 17:35:23 - component-loader - INFO - Found class ConditionalNode in module app.components.control_flow.conditionalNode
2025-07-04 17:35:23 - component-loader - INFO - Found class IDGeneratorComponent in module app.components.helper.id_generator
2025-07-04 17:35:23 - component-loader - INFO - Found class DocExtractorComponent in module app.components.helper.doc_extractor
2025-07-04 17:35:23 - component-loader - INFO - Loaded 38 component modules
2025-07-04 17:35:23 - workflow-service - INFO - Successfully loaded 38 component modules
2025-07-04 17:35:26 - workflow-service - INFO - WorkflowService initialized
Workflow service started on port 50056
x2025-07-04 17:39:21 - workflow-service - INFO - updateWorkflow request received
2025-07-04 17:39:21 [info     ] update_workflow_request        update_mask=['name', 'description', 'workflow_data', 'start_nodes'] workflow_id=973a0c0b-3f8c-41da-b39d-1a7a5440cf5f
[DEBUG] 'workflow_data' is in update_mask. Processing...
[DEBUG] Successfully parsed workflow_data JSON for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'start-node', 'type': 'WorkflowNode', 'position': {'x': -120, 'y': 140}, 'data': {'label': 'Start', 'type': 'component', 'originalType': 'StartNode', 'definition': {'name': 'StartNode', 'display_name': 'Start', 'description': 'The starting point for all workflows. Only nodes connected to this node will be executed.', 'category': 'Input/Output', 'icon': 'Play', 'beta': False, 'inputs': [], 'outputs': [{'name': 'flow', 'display_name': 'Flow', 'output_type': 'Any'}], 'is_valid': True, 'path': 'components.io.start_node'}, 'config': {'collected_parameters': {'MergeDataComponent-1751626170252_main_input': {'node_id': 'MergeDataComponent-1751626170252', 'node_name': 'Merge Data', 'input_name': 'main_input', 'connected_to_start': True, 'required': True, 'input_type': 'dict', 'options': None}}}}, 'width': 208, 'height': 124, 'selected': False, 'dragging': False}, {'id': 'AgenticAI-1751521933685', 'type': 'WorkflowNode', 'position': {'x': 400, 'y': -100}, 'data': {'label': 'AI Agent Executor', 'type': 'agent', 'originalType': 'AgenticAI', 'definition': {'name': 'AgenticAI', 'display_name': 'AI Agent Executor', 'description': 'Executes an AI agent with tools and memory using AutoGen.', 'category': 'AI', 'icon': 'Bot', 'beta': True, 'requires_approval': False, 'inputs': [{'name': 'model_provider', 'display_name': 'Model Provider', 'info': 'The AI model provider to use.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'alibaba', 'options': ['alibaba', 'anthropic', 'cline', 'coding', 'deepinfra', 'deepseek', 'google', 'groq', 'minimaxi', 'mistral', 'nebius', 'netmind', 'novita', 'openai', 'parasail', 'perplexity', 'together', 'vertex', 'xai', 'Custom'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'base_url', 'display_name': 'Base URL', 'info': 'Base URL for the API (leave empty for default provider URL).', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'model_provider', 'field_value': 'Custom', 'operator': 'equals'}, {'field_name': 'model_provider', 'field_value': 'Azure OpenAI', 'operator': 'equals'}, {'field_name': 'model_provider', 'field_value': 'Ollama', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'model_name', 'display_name': 'Model', 'info': 'Select the model to use. The list is dynamically fetched from the model provider API when you select a provider.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': [], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR', '_dynamicFiltering': True, '_filterByField': 'model_provider', '_providerIdMapping': {'alibaba': {'providerId': '137fa03d-a590-45cb-98b3-156c3a734740', 'providerName': 'alibaba', 'isActive': True}, 'anthropic': {'providerId': 'aea5be12-9aea-4e74-85a9-72fb29600079', 'providerName': 'anthropic', 'isActive': True}, 'cline': {'providerId': '661aaecf-c7a1-4166-bb5a-bb8300ada45b', 'providerName': 'cline', 'isActive': True}, 'coding': {'providerId': '152ad359-213b-41a5-951a-3057de036307', 'providerName': 'coding', 'isActive': True}, 'deepinfra': {'providerId': '5fa7e842-3644-464d-af18-d188c81d7b45', 'providerName': 'deepinfra', 'isActive': True}, 'deepseek': {'providerId': '59605e2b-5a8a-4443-b9a8-3b86015712ef', 'providerName': 'deepseek', 'isActive': True}, 'google': {'providerId': '34178783-8099-4da9-b17d-9b65611136d6', 'providerName': 'google', 'isActive': True}, 'groq': {'providerId': 'babd6416-991d-4d44-a70d-a6be0654b1a7', 'providerName': 'groq', 'isActive': True}, 'minimaxi': {'providerId': 'a59f4d27-c58f-463b-a16c-899a22091cb1', 'providerName': 'minimaxi', 'isActive': True}, 'mistral': {'providerId': '996804db-2ac8-4c62-a9fe-53ee943f8b7a', 'providerName': 'mistral', 'isActive': True}, 'nebius': {'providerId': '17d2cd04-c0dc-4b08-a048-a7f473243ebb', 'providerName': 'nebius', 'isActive': True}, 'netmind': {'providerId': 'efad9b08-b481-4040-b57f-873063a36beb', 'providerName': 'netmind', 'isActive': True}, 'novita': {'providerId': 'acb072e1-f979-4472-840c-8b499734369d', 'providerName': 'novita', 'isActive': True}, 'openai': {'providerId': 'be157187-39c0-4930-9ca9-3d127d464a28', 'providerName': 'openai', 'isActive': True}, 'parasail': {'providerId': '9dbc0526-2c57-4df8-91c4-3ef94972bd7c', 'providerName': 'parasail', 'isActive': True}, 'perplexity': {'providerId': '9d918de2-ec13-4fa8-bf88-df28bf37ac08', 'providerName': 'perplexity', 'isActive': True}, 'together': {'providerId': '4e61f654-af7a-4e31-80c8-64186772c0d3', 'providerName': 'together', 'isActive': True}, 'vertex': {'providerId': 'f8cbad74-fdad-4990-a270-9ef90fcf27ea', 'providerName': 'vertex', 'isActive': True}, 'xai': {'providerId': 'f0dc3323-1eff-4acd-9214-5fbb1440f5dc', 'providerName': 'xai', 'isActive': True}, 'Custom': {'providerId': 'custom', 'providerName': 'Custom', 'isActive': True}}}, {'name': 'temperature', 'display_name': 'Temperature', 'info': 'Controls randomness: 0 is deterministic, higher values are more random.', 'input_type': 'float', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 0.7, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'description', 'display_name': 'Description', 'info': 'Description of the agent for UI display.', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'execution_type', 'display_name': 'Execution Type', 'info': 'Determines if agent handles single response or multi-turn conversation.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'response', 'options': ['response', 'interactive'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'query', 'display_name': 'Query/Objective', 'info': 'The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'system_message', 'display_name': 'System Message', 'info': 'System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.', 'input_type': 'multiline', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'termination_condition', 'display_name': 'Termination Condition', 'info': 'Defines when multi-turn conversations should end. Required for interactive execution type.', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': [{'field_name': 'execution_type', 'field_value': 'interactive', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'max_tokens', 'display_name': 'Max Tokens', 'info': 'Maximum response length in tokens.', 'input_type': 'int', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 1000, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_variables', 'display_name': 'Input Variables', 'info': 'Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'tools', 'display_name': 'Tools', 'info': 'Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.', 'input_type': 'handle', 'input_types': ['Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'memory', 'display_name': 'Memory Object', 'info': 'Connect a memory object from another node.', 'input_type': 'handle', 'input_types': ['Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'autogen_agent_type', 'display_name': 'AutoGen Agent Type', 'info': 'The type of AutoGen agent to create internally.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': True, 'value': 'Assistant', 'options': ['Assistant', 'UserProxy', 'CodeExecutor'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}], 'outputs': [{'name': 'final_answer', 'display_name': 'Final Answer', 'output_type': 'string', 'semantic_type': None, 'method': None}, {'name': 'intermediate_steps', 'display_name': 'Intermediate Steps', 'output_type': 'list', 'semantic_type': None, 'method': None}, {'name': 'updated_memory', 'display_name': 'Updated Memory', 'output_type': 'Memory', 'semantic_type': None, 'method': None}, {'name': 'error', 'display_name': 'Error', 'output_type': 'str', 'semantic_type': None, 'method': None}], 'is_valid': True, 'path': 'components.ai.agenticai', 'interface_issues': [], '_enhanced': True, '_enhancementTimestamp': 1751521718367, '_providerIdMapping': {'alibaba': {'providerId': '137fa03d-a590-45cb-98b3-156c3a734740', 'providerName': 'alibaba', 'isActive': True}, 'anthropic': {'providerId': 'aea5be12-9aea-4e74-85a9-72fb29600079', 'providerName': 'anthropic', 'isActive': True}, 'cline': {'providerId': '661aaecf-c7a1-4166-bb5a-bb8300ada45b', 'providerName': 'cline', 'isActive': True}, 'coding': {'providerId': '152ad359-213b-41a5-951a-3057de036307', 'providerName': 'coding', 'isActive': True}, 'deepinfra': {'providerId': '5fa7e842-3644-464d-af18-d188c81d7b45', 'providerName': 'deepinfra', 'isActive': True}, 'deepseek': {'providerId': '59605e2b-5a8a-4443-b9a8-3b86015712ef', 'providerName': 'deepseek', 'isActive': True}, 'google': {'providerId': '34178783-8099-4da9-b17d-9b65611136d6', 'providerName': 'google', 'isActive': True}, 'groq': {'providerId': 'babd6416-991d-4d44-a70d-a6be0654b1a7', 'providerName': 'groq', 'isActive': True}, 'minimaxi': {'providerId': 'a59f4d27-c58f-463b-a16c-899a22091cb1', 'providerName': 'minimaxi', 'isActive': True}, 'mistral': {'providerId': '996804db-2ac8-4c62-a9fe-53ee943f8b7a', 'providerName': 'mistral', 'isActive': True}, 'nebius': {'providerId': '17d2cd04-c0dc-4b08-a048-a7f473243ebb', 'providerName': 'nebius', 'isActive': True}, 'netmind': {'providerId': 'efad9b08-b481-4040-b57f-873063a36beb', 'providerName': 'netmind', 'isActive': True}, 'novita': {'providerId': 'acb072e1-f979-4472-840c-8b499734369d', 'providerName': 'novita', 'isActive': True}, 'openai': {'providerId': 'be157187-39c0-4930-9ca9-3d127d464a28', 'providerName': 'openai', 'isActive': True}, 'parasail': {'providerId': '9dbc0526-2c57-4df8-91c4-3ef94972bd7c', 'providerName': 'parasail', 'isActive': True}, 'perplexity': {'providerId': '9d918de2-ec13-4fa8-bf88-df28bf37ac08', 'providerName': 'perplexity', 'isActive': True}, 'together': {'providerId': '4e61f654-af7a-4e31-80c8-64186772c0d3', 'providerName': 'together', 'isActive': True}, 'vertex': {'providerId': 'f8cbad74-fdad-4990-a270-9ef90fcf27ea', 'providerName': 'vertex', 'isActive': True}, 'xai': {'providerId': 'f0dc3323-1eff-4acd-9214-5fbb1440f5dc', 'providerName': 'xai', 'isActive': True}, 'Custom': {'providerId': 'custom', 'providerName': 'Custom', 'isActive': True}}, '_availableCredentials': []}, 'config': {'id': 'AgenticAI-1751521933685', 'name': 'AI Agent Executor', 'model_provider': 'openai', 'base_url': '', 'model_name': 'gpt-4o-mini', 'temperature': 0.7, 'description': 'response generator', 'execution_type': 'response', 'query': 'Research investments 2024 2025 portfolio companies for given Investor and VC Firm.', 'system_message': 'You are a research assistant specializing in venture capital investments. Provide detailed, factual information about investor activities and portfolio companies.\n\nInvestor Name - ${{investor_name}} \nVC Firm - ${{vc_firm}} \n\n\nAlways return response in following format - \n\n{\n    "result": {\n             "data" : "response of LLM here.... as string"\n     },\n     "investor_name" : "investor name which is given as input parameter",\n     "vc_firm": "vc firm name is given as input parameter"\n\n}', 'termination_condition': '', 'max_tokens': 1000, 'input_variables': {}, 'autogen_agent_type': 'Assistant', 'tools': []}}, 'width': 208, 'height': 234, 'selected': False, 'dragging': False, 'style': {'opacity': 1}}, {'id': 'MergeDataComponent-1751626170252', 'type': 'WorkflowNode', 'position': {'x': 60, 'y': -80}, 'data': {'label': 'Merge Data', 'type': 'component', 'originalType': 'MergeDataComponent', 'definition': {'name': 'MergeDataComponent', 'display_name': 'Merge Data', 'description': 'Combines multiple dictionaries or lists.', 'category': 'Processing', 'icon': 'Combine', 'beta': False, 'requires_approval': False, 'inputs': [{'name': 'main_input', 'display_name': 'Main Input', 'info': 'The main data structure to merge. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'list', 'Any'], 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'num_additional_inputs', 'display_name': 'Number of Additional Inputs', 'info': 'Set the number of additional inputs to show (1-10).', 'input_type': 'int', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 2, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'merge_strategy', 'display_name': 'Merge Strategy (Dicts)', 'info': "How to handle conflicts when merging dictionaries. 'Aggregate' will combine conflicting values into a list. 'Structured Compose' creates custom key-value pairs.", 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'Overwrite', 'options': ['Overwrite', 'Deep Merge', 'Error on Conflict', 'Aggregate', 'Structured Compose'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'output_key_1', 'display_name': 'Output Key 1', 'info': "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'output_key_2', 'display_name': 'Output Key 2', 'info': "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'output_key_3', 'display_name': 'Output Key 3', 'info': "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'output_key_4', 'display_name': 'Output Key 4', 'info': "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'output_key_5', 'display_name': 'Output Key 5', 'info': "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'output_key_6', 'display_name': 'Output Key 6', 'info': "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'output_key_7', 'display_name': 'Output Key 7', 'info': "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'output_key_8', 'display_name': 'Output Key 8', 'info': "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'output_key_9', 'display_name': 'Output Key 9', 'info': "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'output_key_10', 'display_name': 'Output Key 10', 'info': "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'output_key_11', 'display_name': 'Output Key 11', 'info': "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy.", 'input_type': 'string', 'input_types': ['string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '', 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_1', 'display_name': 'Input 1', 'info': 'Data structure 1 to merge. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '1', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '2', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '3', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '4', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_2', 'display_name': 'Input 2', 'info': 'Data structure 2 to merge. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '2', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '3', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '4', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_3', 'display_name': 'Input 3', 'info': 'Data structure 3 to merge. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '3', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '4', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_4', 'display_name': 'Input 4', 'info': 'Data structure 4 to merge. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '4', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_5', 'display_name': 'Input 5', 'info': 'Data structure 5 to merge. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '5', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_6', 'display_name': 'Input 6', 'info': 'Data structure 6 to merge. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '6', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_7', 'display_name': 'Input 7', 'info': 'Data structure 7 to merge. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '7', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_8', 'display_name': 'Input 8', 'info': 'Data structure 8 to merge. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '8', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_9', 'display_name': 'Input 9', 'info': 'Data structure 9 to merge. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '9', 'operator': 'equals'}, {'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'input_10', 'display_name': 'Input 10', 'info': 'Data structure 10 to merge. Can be connected from another node or entered directly.', 'input_type': 'dict', 'input_types': ['dict', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': {}, 'options': None, 'visibility_rules': [{'field_name': 'num_additional_inputs', 'field_value': '10', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}], 'outputs': [{'name': 'output_data', 'display_name': 'Merged Data', 'output_type': 'Any', 'semantic_type': None, 'method': None}, {'name': 'error', 'display_name': 'Error', 'output_type': 'str', 'semantic_type': None, 'method': None}], 'is_valid': True, 'path': 'components.processing.mergedatacomponent', 'interface_issues': []}, 'config': {}}, 'width': 208, 'height': 150, 'selected': False, 'dragging': False, 'style': {'opacity': 1}}], 'edges': [{'id': 'reactflow__edge-MergeDataComponent-1751626170252output_data-AgenticAI-1751521933685input_variables', 'source': 'MergeDataComponent-1751626170252', 'sourceHandle': 'output_data', 'target': 'AgenticAI-1751521933685', 'targetHandle': 'input_variables', 'type': 'default', 'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'opacity': 1, 'zIndex': 5}}, {'id': 'reactflow__edge-start-nodeflow-MergeDataComponent-1751626170252main_input', 'source': 'start-node', 'sourceHandle': 'flow', 'target': 'MergeDataComponent-1751626170252', 'targetHandle': 'main_input', 'type': 'default', 'animated': True, 'style': {'strokeWidth': 2, 'stroke': 'var(--primary)', 'opacity': 1, 'zIndex': 5}}], 'unconnected_nodes': [{'id': 'MCP_Code-Runnder-Mcp_execute_code-1751531763417', 'type': 'WorkflowNode', 'position': {'x': 460, 'y': -600}, 'data': {'label': 'Code-Runnder-Mcp - execute_code', 'type': 'mcp', 'originalType': 'MCP_Code-Runnder-Mcp_execute_code', 'definition': {'name': 'MCP_Code-Runnder-Mcp_execute_code', 'display_name': 'Code-Runnder-Mcp - execute_code', 'description': 'Execute JavaScript or Python code securely with comprehensive error handling and security measures', 'category': 'MCP Marketplace', 'icon': 'Cloud', 'beta': True, 'inputs': [{'name': 'language', 'display_name': 'language', 'info': 'Programming language to execute', 'input_type': 'dropdown', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': ['javascript', 'python'], 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'code', 'display_name': 'code', 'info': 'Code to execute', 'input_type': 'string', 'input_types': None, 'required': True, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'input', 'display_name': 'input', 'info': 'Input data for the program (stdin)', 'input_type': 'string', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'timeout', 'display_name': 'timeout', 'info': 'Execution timeout in milliseconds (max 60000)', 'input_type': 'number', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'memoryLimit', 'display_name': 'memoryLimit', 'info': 'Memory limit in MB (max 512)', 'input_type': 'number', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}, {'name': 'enableNetworking', 'display_name': 'enableNetworking', 'info': 'Enable network access for this execution', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': None, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR'}], 'outputs': [{'name': 'jsonrpc', 'display_name': 'jsonrpc', 'output_type': 'string'}, {'name': 'id', 'display_name': 'id', 'output_type': 'number'}, {'name': 'result', 'display_name': 'result', 'output_type': 'object'}], 'is_valid': True, 'path': 'mcp.mcp_code-runnder-mcp_execute_code', 'type': 'MCP', 'env_keys': [], 'env_credential_status': 'pending_input', 'mcp_info': {'server_id': 'f506eb7c-a648-4873-8055-e2273be08478', 'server_path': '', 'tool_name': 'execute_code', 'input_schema': {'type': 'object', 'properties': {'language': {'type': 'string', 'enum': ['javascript', 'python'], 'description': 'Programming language to execute'}, 'code': {'type': 'string', 'description': 'Code to execute'}, 'input': {'type': 'string', 'description': 'Input data for the program (stdin)'}, 'timeout': {'type': 'number', 'description': 'Execution timeout in milliseconds (max 60000)'}, 'memoryLimit': {'type': 'number', 'description': 'Memory limit in MB (max 512)'}, 'enableNetworking': {'type': 'boolean', 'description': 'Enable network access for this execution'}}, 'required': ['language', 'code'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}, 'output_schema': {'type': 'object', 'properties': {'jsonrpc': {'type': 'string', 'enum': ['2.0']}, 'id': {'type': 'integer'}, 'result': {'type': 'object', 'properties': {'content': {'type': 'array', 'items': {'type': 'object', 'properties': {'type': {'type': 'string', 'enum': ['text']}, 'text': {'type': 'string', 'description': 'Stringified JSON object containing script execution result'}}, 'required': ['type', 'text']}}}, 'required': ['content']}}, 'required': ['jsonrpc', 'id', 'result']}}}, 'config': {}}, 'width': 208, 'height': 262, 'selected': False, 'dragging': False, 'style': {'opacity': 0.5}}, {'id': 'LoopNode-1751625968936', 'type': 'WorkflowNode', 'position': {'x': 180, 'y': -380}, 'data': {'label': 'For Each Loop', 'type': 'loop', 'originalType': 'LoopNode', 'definition': {'name': 'LoopNode', 'display_name': 'For Each Loop', 'description': 'Iterates over a list, with advanced controls for parallelism, aggregation, and error handling.', 'category': 'Logic', 'icon': 'Repeat', 'beta': False, 'requires_approval': False, 'inputs': [{'name': 'source_type', 'display_name': 'Iteration Source', 'info': 'Choose whether to iterate over a list of items or a number range.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': True, 'advanced': False, 'value': 'iteration_list', 'options': ['iteration_list', 'number_range'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'iteration_list', 'display_name': 'Iteration List', 'info': 'The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array.', 'input_type': 'list', 'input_types': ['array', 'list', 'Any'], 'required': False, 'is_handle': True, 'is_list': True, 'real_time_refresh': False, 'advanced': False, 'value': [], 'options': None, 'visibility_rules': [{'field_name': 'source_type', 'field_value': 'iteration_list', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': [{'field_name': 'source_type', 'field_value': 'iteration_list', 'operator': 'equals'}], 'requirement_logic': 'OR'}, {'name': 'batch_size', 'display_name': 'Batch Size', 'info': 'Number of items to process together in each iteration. For example, batch size 2 will process items in pairs: [item1, item2], [item3, item4], etc.', 'input_type': 'string', 'input_types': ['number', 'integer', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '1', 'options': None, 'visibility_rules': [{'field_name': 'source_type', 'field_value': 'iteration_list', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'start', 'display_name': 'Start Number', 'info': 'Starting number for the range. Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['number', 'integer', 'string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '1', 'options': None, 'visibility_rules': [{'field_name': 'source_type', 'field_value': 'number_range', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': [{'field_name': 'source_type', 'field_value': 'number_range', 'operator': 'equals'}], 'requirement_logic': 'OR'}, {'name': 'end', 'display_name': 'End Number', 'info': 'Ending number for the range (inclusive). Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['number', 'integer', 'string', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '10', 'options': None, 'visibility_rules': [{'field_name': 'source_type', 'field_value': 'number_range', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': [{'field_name': 'source_type', 'field_value': 'number_range', 'operator': 'equals'}], 'requirement_logic': 'OR'}, {'name': 'step', 'display_name': 'Step Size', 'info': 'Step size for the range (default: 1). Can be connected from another node or entered directly.', 'input_type': 'string', 'input_types': ['number', 'integer', 'Any'], 'required': False, 'is_handle': True, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': '1', 'options': None, 'visibility_rules': [{'field_name': 'source_type', 'field_value': 'number_range', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'parallel_execution', 'display_name': 'Parallel Execution', 'info': 'Execute loop iterations in parallel for better performance.', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': True, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'max_concurrent', 'display_name': 'Max Concurrent Iterations', 'info': 'Maximum number of iterations to run concurrently (1-20).', 'input_type': 'int', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 3, 'options': None, 'visibility_rules': [{'field_name': 'parallel_execution', 'field_value': 'True', 'operator': 'equals'}], 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'preserve_order', 'display_name': 'Preserve Order', 'info': 'Maintain the original order of items in the results.', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': True, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'iteration_timeout', 'display_name': 'Iteration Timeout (seconds)', 'info': 'Maximum time to wait for each iteration to complete (1-3600 seconds).', 'input_type': 'int', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': True, 'value': 60, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'aggregation_type', 'display_name': 'Result Aggregation', 'info': 'How to aggregate results from all iterations.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'collect_all', 'options': ['collect_all', 'collect_successful', 'count_only', 'latest_only', 'first_success', 'combine_text'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'include_metadata', 'display_name': 'Include Metadata', 'info': 'Include metadata (timing, iteration index, etc.) in results.', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': True, 'value': True, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'on_iteration_error', 'display_name': 'On Iteration Error', 'info': 'How to handle errors in individual iterations.', 'input_type': 'dropdown', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': False, 'value': 'continue', 'options': ['continue', 'retry_once', 'retry_twice', 'exit_loop'], 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}, {'name': 'include_errors', 'display_name': 'Include Errors in Results', 'info': 'Include error information in the final results.', 'input_type': 'bool', 'input_types': None, 'required': False, 'is_handle': False, 'is_list': False, 'real_time_refresh': False, 'advanced': True, 'value': True, 'options': None, 'visibility_rules': None, 'visibility_logic': 'OR', 'requirement_rules': None, 'requirement_logic': 'OR'}], 'outputs': [{'name': 'current_item', 'display_name': 'Current Item (Iteration Output)', 'output_type': 'object', 'semantic_type': None, 'method': None}, {'name': 'final_results', 'display_name': 'All Results (Exit Output)', 'output_type': 'array', 'semantic_type': None, 'method': None}], 'is_valid': True, 'path': 'components.logic.loopnode', 'interface_issues': []}, 'config': {'source_type': 'iteration_list', 'iteration_list': [], 'batch_size': '1', 'start': '1', 'end': '10', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}}, 'width': 208, 'height': 186, 'selected': False, 'dragging': False, 'style': {'opacity': 0.5}}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflow_builders/66a154d5-7821-492c-a888-106018ff5f2e.json
[DEBUG] GCS builder upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflow_builders/66a154d5-7821-492c-a888-106018ff5f2e.json
[DEBUG] Starting workflow conversion for PATCH
[DEBUG] Workflow data keys: ['nodes', 'edges', 'unconnected_nodes']
[DEBUG] Number of nodes: 3
[DEBUG] Number of edges: 2
[DEBUG] Node 0: id=start-node, type=component, originalType=StartNode
[DEBUG] Node 1: id=AgenticAI-1751521933685, type=agent, originalType=AgenticAI
[DEBUG] Node 2: id=MergeDataComponent-1751626170252, type=component, originalType=MergeDataComponent
[DEBUG] Edge 0: id=reactflow__edge-MergeDataComponent-1751626170252output_data-AgenticAI-1751521933685input_variables, source=MergeDataComponent-1751626170252, target=AgenticAI-1751521933685, sourceHandle=output_data
[DEBUG] Edge 1: id=reactflow__edge-start-nodeflow-MergeDataComponent-1751626170252main_input, source=start-node, target=MergeDataComponent-1751626170252, sourceHandle=flow
[DEBUG] Preprocessing workflow data for PATCH to handle JSON serialization
[DEBUG] Workflow data preprocessing completed for PATCH
Extracting available nodes...
[TRANSITION_ID] transition-start-node
[TRANSITION_ID] transition-AgenticAI-1751521933685
[TRANSITION_ID] transition-MergeDataComponent-1751626170252
Available nodes: [{'name': 'AgenticAI', 'display_name': 'AI Agent Executor', 'type': 'agent', 'transition_id': 'transition-AgenticAI-1751521933685', 'label': 'AI Agent Executor'}, {'name': 'MergeDataComponent', 'display_name': 'Merge Data', 'type': 'component', 'transition_id': 'transition-MergeDataComponent-1751626170252', 'label': 'Merge Data'}]
[DEBUG] Available nodes extracted for PATCH
[DEBUG] Validating template variables for PATCH
[DEBUG] Template variable validation successful for PATCH: 2 variables found

================================================================================
🚀 STARTING WORKFLOW CONVERSION TO TRANSITION SCHEMA
================================================================================
📊 WORKFLOW COMPONENTS EXTRACTED:
   - Nodes: 3
   - Edges: 2
   - MCP Configs: 0

🔧 CHECKING FOR TOOL NODES IN WORKFLOW...
   ℹ️  No separate tool nodes found, creating virtual nodes from config...
      🔍 Checking AgenticAI node: AgenticAI-1751521933685
         - Config keys: ['id', 'name', 'model_provider', 'base_url', 'model_name', 'temperature', 'description', 'execution_type', 'query', 'system_message', 'termination_condition', 'max_tokens', 'input_variables', 'autogen_agent_type', 'tools']
         - No tools found in config.tools
         - Total tool connections to process: 0
   ℹ️  No tool connections found in AgenticAI configs
[DEBUG] is_conditional_node(start-node): node_type='component', original_type='StartNode', result=False
[DEBUG] is_conditional_node(AgenticAI-1751521933685): node_type='agent', original_type='AgenticAI', result=False
[DEBUG] is_conditional_node(MergeDataComponent-1751626170252): node_type='component', original_type='MergeDataComponent', result=False
📋 NODE TYPE BREAKDOWN:
   - StartNode: 1
   - AgenticAI: 1
   - MergeDataComponent: 1
ℹ️  NO CONDITIONAL NODES DETECTED

🔍 VALIDATING HANDLE MAPPINGS...
✅ Handle mapping validation successful

🎯 IDENTIFYING START NODE...
   Checking node 0: start-node (type: StartNode)
✅ Found start node: start-node

🔗 FINDING NODES CONNECTED TO START NODE...
   - MergeDataComponent-1751626170252
✅ Found 1 nodes connected to start

🏗️  BUILDING WORKFLOW GRAPH...
   - Graph nodes: 2
   - Edge mappings: 2
   - All nodes: 3
   - Removed start node from graph: start-node

📊 COMPUTING NODE LEVELS...
   - Node levels computed for 2 nodes
   - Grouped into 2 levels

🔍 IDENTIFYING TOOL NODES...
      🔍 Found 0 tool nodes that will be integrated into agents
   🔧 INTEGRATING TOOLS INTO AGENT CONFIGURATIONS...

🔄 PHASE 1: CONVERTING NODES TO TRANSITION SCHEMA FORMAT
============================================================

📦 Processing node 1/3: start-node
   Type: StartNode (component)
   ⏭️  SKIPPED: Start node (will not appear in final schema)

📦 Processing node 2/3: AgenticAI-1751521933685
   Type: AgenticAI (agent)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(AgenticAI-1751521933685): node_type='agent', original_type='AgenticAI', result=False
         ⏭️  SKIPPED: Tool input field (tools should be callable functions, not input sources)
   ✅ CONVERTED: Added to transition_nodes array

📦 Processing node 3/3: MergeDataComponent-1751626170252
   Type: MergeDataComponent (component)
   ✅ PROCESSING: Component node
[DEBUG] is_conditional_node(MergeDataComponent-1751626170252): node_type='component', original_type='MergeDataComponent', result=False
   ✅ CONVERTED: Added to transition_nodes array

🔗 COMBINING DUPLICATE NODES...
   No duplicate nodes found (2 nodes)

📊 PHASE 1 SUMMARY:
   - Start nodes skipped: 1 ['start-node']
   - Tool nodes skipped (integrated into agents): 0 []
   - Component nodes processed: 2 ['AgenticAI-1751521933685', 'MergeDataComponent-1751626170252']
   - Final transition_nodes count: 2
   - Agent tool integrations: 0 agents with tools

🔄 PHASE 2: CREATING TRANSITIONS FROM WORKFLOW LOGIC
============================================================
🎯 Start node marked as processed: start-node

🏗️  PROCESSING LEVEL 0
   Nodes at this level: ['MergeDataComponent-1751626170252']

   📦 Processing node: MergeDataComponent-1751626170252
      Type: MergeDataComponent (component)
[DEBUG] is_conditional_node(MergeDataComponent-1751626170252): node_type='component', original_type='MergeDataComponent', result=False
      Is conditional: False
[DEBUG] is_output_node(MergeDataComponent-1751626170252): node_type='component', result=False
[DEBUG] is_conditional_node(AgenticAI-1751521933685): node_type='agent', original_type='AgenticAI', result=False
      ✅ REGULAR TRANSITION CREATED:
         - ID: transition-MergeDataComponent-1751626170252
         - Sequence: 1
         - Execution Type: Components
         - Tools: 1
         - Input Data: 0
         - Output Data: 1

🏗️  PROCESSING LEVEL 1
   Nodes at this level: ['AgenticAI-1751521933685']

   📦 Processing node: AgenticAI-1751521933685
      Type: AgenticAI (agent)
[DEBUG] is_conditional_node(AgenticAI-1751521933685): node_type='agent', original_type='AgenticAI', result=False
      Is conditional: False
[DEBUG] is_output_node(AgenticAI-1751521933685): node_type='agent', result=False

================================================================================
🎉 WORKFLOW CONVERSION COMPLETED SUCCESSFULLY
================================================================================
📊 FINAL STATISTICS:
   - Total nodes in final schema: 2
   - Total transitions created: 2
   - Conditional transitions: 0
   - Regular transitions: 1

⚙️  REGULAR TRANSITIONS CREATED:
   - MergeDataComponent-1751626170252: 1 tools

🔍 SCHEMA VALIDATION:
   ✅ ALL SCHEMA VALIDATION CHECKS PASSED!

🚀 CONVERSION COMPLETE - SCHEMA READY FOR EXECUTION
================================================================================
[DEBUG] Workflow conversion successful for PATCH
✅ Transition schema is valid.
[DEBUG] Transition schema validation successful for PATCH
[DEBUG] Received JSON data: {'nodes': [{'id': 'AgenticAI', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'AgenticAI', 'input_schema': {'predefined_fields': [{'field_name': 'model_provider', 'data_type': {'type': 'string', 'description': 'The AI model provider to use.'}, 'required': False}, {'field_name': 'base_url', 'data_type': {'type': 'string', 'description': 'Base URL for the API (leave empty for default provider URL).'}, 'required': False}, {'field_name': 'model_name', 'data_type': {'type': 'string', 'description': 'Select the model to use. The list is dynamically fetched from the model provider API when you select a provider.'}, 'required': False}, {'field_name': 'temperature', 'data_type': {'type': 'number', 'description': 'Controls randomness: 0 is deterministic, higher values are more random.'}, 'required': False}, {'field_name': 'description', 'data_type': {'type': 'string', 'description': 'Description of the agent for UI display.'}, 'required': False}, {'field_name': 'execution_type', 'data_type': {'type': 'string', 'description': 'Determines if agent handles single response or multi-turn conversation.'}, 'required': False}, {'field_name': 'query', 'data_type': {'type': 'string', 'description': 'The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.'}, 'required': True}, {'field_name': 'system_message', 'data_type': {'type': 'string', 'description': 'System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'termination_condition', 'data_type': {'type': 'string', 'description': 'Defines when multi-turn conversations should end. Required for interactive execution type.'}, 'required': False}, {'field_name': 'max_tokens', 'data_type': {'type': 'number', 'description': 'Maximum response length in tokens.'}, 'required': False}, {'field_name': 'input_variables', 'data_type': {'type': 'object', 'description': 'Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'memory', 'data_type': {'type': 'string', 'description': 'Connect a memory object from another node.'}, 'required': False}, {'field_name': 'autogen_agent_type', 'data_type': {'type': 'string', 'description': 'The type of AutoGen agent to create internally.'}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'final_answer', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}, {'field_name': 'intermediate_steps', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}, {'field_name': 'updated_memory', 'data_type': {'type': 'string', 'description': '', 'format': 'datetime'}}, {'field_name': 'error', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}]}}]}, {'id': 'MergeDataComponent', 'server_script_path': '', 'server_tools': [{'tool_id': 1, 'tool_name': 'MergeDataComponent', 'input_schema': {'predefined_fields': [{'field_name': 'main_input', 'data_type': {'type': 'object', 'description': 'The main data structure to merge. Can be connected from another node or entered directly.'}, 'required': True}, {'field_name': 'num_additional_inputs', 'data_type': {'type': 'number', 'description': 'Set the number of additional inputs to show (1-10).'}, 'required': False}, {'field_name': 'merge_strategy', 'data_type': {'type': 'string', 'description': "How to handle conflicts when merging dictionaries. 'Aggregate' will combine conflicting values into a list. 'Structured Compose' creates custom key-value pairs."}, 'required': False}, {'field_name': 'output_key_1', 'data_type': {'type': 'string', 'description': "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy."}, 'required': False}, {'field_name': 'output_key_2', 'data_type': {'type': 'string', 'description': "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy."}, 'required': False}, {'field_name': 'output_key_3', 'data_type': {'type': 'string', 'description': "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy."}, 'required': False}, {'field_name': 'output_key_4', 'data_type': {'type': 'string', 'description': "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy."}, 'required': False}, {'field_name': 'output_key_5', 'data_type': {'type': 'string', 'description': "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy."}, 'required': False}, {'field_name': 'output_key_6', 'data_type': {'type': 'string', 'description': "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy."}, 'required': False}, {'field_name': 'output_key_7', 'data_type': {'type': 'string', 'description': "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy."}, 'required': False}, {'field_name': 'output_key_8', 'data_type': {'type': 'string', 'description': "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy."}, 'required': False}, {'field_name': 'output_key_9', 'data_type': {'type': 'string', 'description': "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy."}, 'required': False}, {'field_name': 'output_key_10', 'data_type': {'type': 'string', 'description': "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy."}, 'required': False}, {'field_name': 'output_key_11', 'data_type': {'type': 'string', 'description': "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy."}, 'required': False}, {'field_name': 'input_1', 'data_type': {'type': 'object', 'description': 'Data structure 1 to merge. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_2', 'data_type': {'type': 'object', 'description': 'Data structure 2 to merge. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_3', 'data_type': {'type': 'object', 'description': 'Data structure 3 to merge. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_4', 'data_type': {'type': 'object', 'description': 'Data structure 4 to merge. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_5', 'data_type': {'type': 'object', 'description': 'Data structure 5 to merge. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_6', 'data_type': {'type': 'object', 'description': 'Data structure 6 to merge. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_7', 'data_type': {'type': 'object', 'description': 'Data structure 7 to merge. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_8', 'data_type': {'type': 'object', 'description': 'Data structure 8 to merge. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_9', 'data_type': {'type': 'object', 'description': 'Data structure 9 to merge. Can be connected from another node or entered directly.'}, 'required': False}, {'field_name': 'input_10', 'data_type': {'type': 'object', 'description': 'Data structure 10 to merge. Can be connected from another node or entered directly.'}, 'required': False}]}, 'output_schema': {'predefined_fields': [{'field_name': 'output_data', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}, {'field_name': 'error', 'data_type': {'type': 'string', 'description': '', 'format': 'string'}}]}}]}], 'transitions': [{'id': 'transition-MergeDataComponent-1751626170252', 'sequence': 1, 'transition_type': 'initial', 'execution_type': 'Components', 'node_info': {'node_id': 'MergeDataComponent', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'MergeDataComponent', 'tool_params': {'items': [{'field_name': 'main_input', 'data_type': 'object', 'field_value': None}, {'field_name': 'num_additional_inputs', 'data_type': 'number', 'field_value': None}, {'field_name': 'merge_strategy', 'data_type': 'string', 'field_value': None}, {'field_name': 'output_key_1', 'data_type': 'string', 'field_value': None}, {'field_name': 'output_key_2', 'data_type': 'string', 'field_value': None}, {'field_name': 'output_key_3', 'data_type': 'string', 'field_value': None}, {'field_name': 'output_key_4', 'data_type': 'string', 'field_value': None}, {'field_name': 'output_key_5', 'data_type': 'string', 'field_value': None}, {'field_name': 'output_key_6', 'data_type': 'string', 'field_value': None}, {'field_name': 'output_key_7', 'data_type': 'string', 'field_value': None}, {'field_name': 'output_key_8', 'data_type': 'string', 'field_value': None}, {'field_name': 'output_key_9', 'data_type': 'string', 'field_value': None}, {'field_name': 'output_key_10', 'data_type': 'string', 'field_value': None}, {'field_name': 'output_key_11', 'data_type': 'string', 'field_value': None}, {'field_name': 'input_1', 'data_type': 'object', 'field_value': None}, {'field_name': 'input_2', 'data_type': 'object', 'field_value': None}, {'field_name': 'input_3', 'data_type': 'object', 'field_value': None}, {'field_name': 'input_4', 'data_type': 'object', 'field_value': None}, {'field_name': 'input_5', 'data_type': 'object', 'field_value': None}, {'field_name': 'input_6', 'data_type': 'object', 'field_value': None}, {'field_name': 'input_7', 'data_type': 'object', 'field_value': None}, {'field_name': 'input_8', 'data_type': 'object', 'field_value': None}, {'field_name': 'input_9', 'data_type': 'object', 'field_value': None}, {'field_name': 'input_10', 'data_type': 'object', 'field_value': None}]}}], 'input_data': [], 'output_data': [{'to_transition_id': 'transition-AgenticAI-1751521933685', 'target_node_id': 'AI Agent Executor', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'output_data', 'result_path': 'output_data', 'edge_id': 'reactflow__edge-MergeDataComponent-1751626170252output_data-AgenticAI-1751521933685input_variables'}]}}]}, 'result_resolution': {'node_type': 'component', 'expected_result_structure': 'direct', 'handle_registry': {'input_handles': [{'handle_id': 'main_input', 'handle_name': 'Main Input', 'data_type': 'object', 'required': True, 'description': 'The main data structure to merge. Can be connected from another node or entered directly.'}, {'handle_id': 'output_key_1', 'handle_name': 'Output Key 1', 'data_type': 'string', 'required': False, 'description': "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy."}, {'handle_id': 'output_key_2', 'handle_name': 'Output Key 2', 'data_type': 'string', 'required': False, 'description': "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy."}, {'handle_id': 'output_key_3', 'handle_name': 'Output Key 3', 'data_type': 'string', 'required': False, 'description': "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy."}, {'handle_id': 'output_key_4', 'handle_name': 'Output Key 4', 'data_type': 'string', 'required': False, 'description': "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy."}, {'handle_id': 'output_key_5', 'handle_name': 'Output Key 5', 'data_type': 'string', 'required': False, 'description': "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy."}, {'handle_id': 'output_key_6', 'handle_name': 'Output Key 6', 'data_type': 'string', 'required': False, 'description': "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy."}, {'handle_id': 'output_key_7', 'handle_name': 'Output Key 7', 'data_type': 'string', 'required': False, 'description': "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy."}, {'handle_id': 'output_key_8', 'handle_name': 'Output Key 8', 'data_type': 'string', 'required': False, 'description': "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy."}, {'handle_id': 'output_key_9', 'handle_name': 'Output Key 9', 'data_type': 'string', 'required': False, 'description': "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy."}, {'handle_id': 'output_key_10', 'handle_name': 'Output Key 10', 'data_type': 'string', 'required': False, 'description': "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy."}, {'handle_id': 'output_key_11', 'handle_name': 'Output Key 11', 'data_type': 'string', 'required': False, 'description': "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy."}, {'handle_id': 'input_1', 'handle_name': 'Input 1', 'data_type': 'object', 'required': False, 'description': 'Data structure 1 to merge. Can be connected from another node or entered directly.'}, {'handle_id': 'input_2', 'handle_name': 'Input 2', 'data_type': 'object', 'required': False, 'description': 'Data structure 2 to merge. Can be connected from another node or entered directly.'}, {'handle_id': 'input_3', 'handle_name': 'Input 3', 'data_type': 'object', 'required': False, 'description': 'Data structure 3 to merge. Can be connected from another node or entered directly.'}, {'handle_id': 'input_4', 'handle_name': 'Input 4', 'data_type': 'object', 'required': False, 'description': 'Data structure 4 to merge. Can be connected from another node or entered directly.'}, {'handle_id': 'input_5', 'handle_name': 'Input 5', 'data_type': 'object', 'required': False, 'description': 'Data structure 5 to merge. Can be connected from another node or entered directly.'}, {'handle_id': 'input_6', 'handle_name': 'Input 6', 'data_type': 'object', 'required': False, 'description': 'Data structure 6 to merge. Can be connected from another node or entered directly.'}, {'handle_id': 'input_7', 'handle_name': 'Input 7', 'data_type': 'object', 'required': False, 'description': 'Data structure 7 to merge. Can be connected from another node or entered directly.'}, {'handle_id': 'input_8', 'handle_name': 'Input 8', 'data_type': 'object', 'required': False, 'description': 'Data structure 8 to merge. Can be connected from another node or entered directly.'}, {'handle_id': 'input_9', 'handle_name': 'Input 9', 'data_type': 'object', 'required': False, 'description': 'Data structure 9 to merge. Can be connected from another node or entered directly.'}, {'handle_id': 'input_10', 'handle_name': 'Input 10', 'data_type': 'object', 'required': False, 'description': 'Data structure 10 to merge. Can be connected from another node or entered directly.'}], 'output_handles': [{'handle_id': 'output_data', 'handle_name': 'Merged Data', 'data_type': 'string', 'description': ''}, {'handle_id': 'error', 'handle_name': 'Error', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'output_data': 'output_data', 'error': 'error'}, 'dynamic_discovery': {'enabled': False, 'fallback_patterns': ['result.output_data', 'output_data.output_data', 'response.output_data', 'data.output_data', 'result.error', 'output_data.error', 'response.error', 'data.error', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': True, 'supports_nested_results': False, 'requires_dynamic_discovery': False, 'primary_output_handle': 'output_data'}}, 'approval_required': False, 'end': False}, {'id': 'transition-AgenticAI-1751521933685', 'sequence': 2, 'transition_type': 'standard', 'execution_type': 'agent', 'node_info': {'node_id': 'AgenticAI', 'tools_to_use': [{'tool_id': 1, 'tool_name': 'AgenticAI', 'tool_params': {'items': [{'field_name': 'agent_type', 'data_type': 'string', 'field_value': 'component'}, {'field_name': 'execution_type', 'data_type': 'string', 'field_value': 'response'}, {'field_name': 'query', 'data_type': 'string', 'field_value': 'Research investments 2024 2025 portfolio companies for given Investor and VC Firm.'}, {'field_name': 'input', 'data_type': 'string', 'field_value': ''}, {'field_name': 'system_message', 'data_type': 'string', 'field_value': 'You are a research assistant specializing in venture capital investments. Provide detailed, factual information about investor activities and portfolio companies.\n\nInvestor Name - ${investor_name} \nVC Firm - ${vc_firm} \n\n\nAlways return response in following format - \n\n{\n    "result": {\n             "data" : "response of LLM here.... as string"\n     },\n     "investor_name" : "investor name which is given as input parameter",\n     "vc_firm": "vc firm name is given as input parameter"\n\n}'}, {'field_name': 'input_variables', 'data_type': 'object', 'field_value': None}, {'field_name': 'agent_config', 'data_type': 'object', 'field_value': {'model_config': {'model_provider': 'openai', 'model': 'gpt-4o-mini', 'temperature': 0.7, 'max_tokens': 1000}, 'description': 'response generator', 'autogen_agent_type': 'Assistant', 'termination_condition': ''}}, {'field_name': 'variables', 'data_type': 'object', 'field_value': {'investor_name': '${investor_name}', 'vc_firm': '${vc_firm}'}}]}}], 'input_data': [{'from_transition_id': 'transition-MergeDataComponent-1751626170252', 'source_node_id': 'Merge Data', 'data_type': 'string', 'handle_mappings': [{'source_transition_id': 'transition-MergeDataComponent-1751626170252', 'source_handle_id': 'output_data', 'target_handle_id': 'input_variables', 'edge_id': 'reactflow__edge-MergeDataComponent-1751626170252output_data-AgenticAI-1751521933685input_variables'}]}], 'output_data': []}, 'result_resolution': {'node_type': 'agent', 'expected_result_structure': 'direct', 'handle_registry': {'input_handles': [{'handle_id': 'query', 'handle_name': 'Query/Objective', 'data_type': 'string', 'required': True, 'description': 'The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.'}, {'handle_id': 'system_message', 'handle_name': 'System Message', 'data_type': 'string', 'required': False, 'description': 'System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly.'}, {'handle_id': 'input_variables', 'handle_name': 'Input Variables', 'data_type': 'object', 'required': False, 'description': 'Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.'}, {'handle_id': 'tools', 'handle_name': 'Tools', 'data_type': 'string', 'required': False, 'description': 'Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.'}, {'handle_id': 'memory', 'handle_name': 'Memory Object', 'data_type': 'string', 'required': False, 'description': 'Connect a memory object from another node.'}], 'output_handles': [{'handle_id': 'final_answer', 'handle_name': 'Final Answer', 'data_type': 'string', 'description': ''}, {'handle_id': 'intermediate_steps', 'handle_name': 'Intermediate Steps', 'data_type': 'string', 'description': ''}, {'handle_id': 'updated_memory', 'handle_name': 'Updated Memory', 'data_type': 'string', 'description': ''}, {'handle_id': 'error', 'handle_name': 'Error', 'data_type': 'string', 'description': ''}]}, 'result_path_hints': {'final_answer': 'final_answer', 'intermediate_steps': 'intermediate_steps', 'updated_memory': 'updated_memory', 'error': 'error'}, 'dynamic_discovery': {'enabled': False, 'fallback_patterns': ['result.final_answer', 'output_data.final_answer', 'response.final_answer', 'data.final_answer', 'result.intermediate_steps', 'output_data.intermediate_steps', 'response.intermediate_steps', 'data.intermediate_steps', 'result.updated_memory', 'output_data.updated_memory', 'response.updated_memory', 'data.updated_memory', 'result.error', 'output_data.error', 'response.error', 'data.error', '{handle_id}', 'result', 'output_data', 'response', 'data', 'result.{handle_id}', 'output_data.{handle_id}', 'result.result', 'response.data', 'content', 'value'], 'validation_rules': [{'rule_type': 'type_check', 'rule_config': {'allowed_types': ['string', 'number', 'object', 'array', 'boolean'], 'reject_null': False, 'reject_undefined': True}}, {'rule_type': 'structure_check', 'rule_config': {'min_depth': 0, 'max_depth': 5, 'allow_nested_objects': True, 'allow_arrays': True}}, {'rule_type': 'content_check', 'rule_config': {'min_length': 0, 'reject_empty_strings': False, 'reject_empty_objects': False, 'reject_empty_arrays': False}}]}, 'extraction_metadata': {'supports_multiple_outputs': True, 'supports_nested_results': False, 'requires_dynamic_discovery': False, 'primary_output_handle': 'final_answer'}}, 'approval_required': False, 'end': True}]} (Type: <class 'dict'>)
[Uploading JSON data to GCS]
[Uploaded JSON to GCS]: https://storage.googleapis.com/ruh-dev/workflows/2bf84d7b-f2a3-4c79-bfee-1d251487ca7b.json
[DEBUG] Converted workflow GCS upload successful for PATCH: https://storage.googleapis.com/ruh-dev/workflows/2bf84d7b-f2a3-4c79-bfee-1d251487ca7b.json
Extracting available nodes...
[TRANSITION_ID] transition-start-node
[TRANSITION_ID] transition-AgenticAI-1751521933685
[TRANSITION_ID] transition-MergeDataComponent-1751626170252
Available nodes: [{'name': 'AgenticAI', 'display_name': 'AI Agent Executor', 'type': 'agent', 'transition_id': 'transition-AgenticAI-1751521933685', 'label': 'AI Agent Executor'}, {'name': 'MergeDataComponent', 'display_name': 'Merge Data', 'type': 'component', 'transition_id': 'transition-MergeDataComponent-1751626170252', 'label': 'Merge Data'}]
[DEBUG] Version-relevant fields changed, setting is_updated=True
2025-07-04 17:39:29 [info     ] Set is_updated=True for workflow 973a0c0b-3f8c-41da-b39d-1a7a5440cf5f due to version-relevant changes
2025-07-04 17:39:29 [info     ] Marketplace-relevant fields changed. Derived workflows will be notified when version is published via createVersionAndPublish.
