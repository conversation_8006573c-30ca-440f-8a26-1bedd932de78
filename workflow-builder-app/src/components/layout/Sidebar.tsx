import React, { useState, useCallback, useMemo, useEffect } from "react";
import { ComponentsApiResponse, ComponentDefinition } from "@/types";
import { Agent } from "@/types/agents";
import { MCP_CATEGORIES, MCP_CATEGORY_LABELS, MCPCategory } from "@/types/mcp";
import { WorkflowListItem } from "@/lib/workflowApi";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CustomScrollArea } from "@/components/ui/custom-scroll-area";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { AddOutputSchemaDialog } from "@/components/modals/AddOutputSchemaDialog";
import { AddEnvKeysDialog } from "@/components/modals/AddEnvKeysDialog";
import { toast } from "sonner";
import {
  Search,
  FileText,
  Database,
  Code,
  Workflow,
  Cog,
  Cpu,
  ArrowRightLeft,
  LogIn,
  Grip,
  Brain,
  Store,
  Bell,
  MessageCircle,
  Share2,
  CloudUpload,
  Settings,
  User,
  Users,
  Layers
} from "lucide-react";
import { fetchAgents } from "@/lib/agentApi";
import Image from "next/image";

// Helper function to generate workflow inputs from start_nodes
const generateWorkflowInputs = (startNodes: any[]) => {
  if (!startNodes || startNodes.length === 0) {
    // Fallback to generic input if no start_nodes
    return [
      {
        name: "input",
        display_name: "Input",
        info: "Input data for the workflow",
        input_type: "object",
        required: true,
        is_handle: true,
        is_list: false,
        real_time_refresh: false,
        advanced: false,
        value: null,
        options: null,
        visibility_rules: null,
        visibility_logic: "OR",
        requirement_rules: null,
        requirement_logic: "OR"
      }
    ];
  }

  // Convert start_nodes to input definitions
  return startNodes.map((startNode, index) => ({
    name: startNode.field || `input_${index}`,
    display_name: startNode.field ? startNode.field.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()) : `Input ${index + 1}`,
    info: `Input field: ${startNode.field || `input_${index}`}`,
    input_type: startNode.type === "string" ? "string" : startNode.type === "number" ? "number" : "string",
    required: true,
    is_handle: true,
    is_list: false,
    real_time_refresh: false,
    advanced: false,
    value: null,
    options: null,
    visibility_rules: null,
    visibility_logic: "OR" as const,
    requirement_rules: null,
    requirement_logic: "OR" as const,
    // Store the transition_id for workflow execution
    transition_id: startNode.transition_id
  }));
};

// Stylized MCP Logo Component
interface MCPLogoProps {
  mcpName: string;
  mcpComponents: any[];
}

const MCPLogo: React.FC<MCPLogoProps> = ({ mcpName, mcpComponents }) => {
  const componentWithLogo = mcpComponents.find(comp => comp.logo);

  if (componentWithLogo?.logo) {
    return (
      <div className="relative h-8 w-8 flex-shrink-0 group">
        <div className="absolute inset-0 rounded-md bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
        <Image
          src={componentWithLogo.logo}
          alt={`${mcpName} logo`}
          width={32}
          height={32}
          className="relative z-10 rounded-md object-contain shadow-sm ring-1 ring-black/5 dark:ring-white/10 group-hover:shadow-md transition-shadow duration-200"
          onError={(e) => {
            // Fallback to Cloud icon with enhanced styling
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = `
                <div class="h-8 w-8 rounded-md bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-800/30 dark:to-indigo-800/30 flex items-center justify-center shadow-sm ring-1 ring-black/5 dark:ring-white/10">
                  <svg class="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                  </svg>
                </div>
              `;
            }
          }}
        />
      </div>
    );
  }

  return (
    <div className="h-8 w-8 rounded-md bg-black/5 dark:bg-white/10  flex items-center justify-center shadow-sm ring-1 ring-black/5 dark:ring-white/10 group-hover:shadow-md transition-shadow duration-200">
      <Layers className="h-5 w-5 text-brand-primary dark:text-brand-secondary" />
    </div>
  );
};

// Stylized Agent Logo Component
interface AgentLogoProps {
  agent: {
    id: string;
    name: string;
    avatar?: string;
  };
}

const AgentLogo: React.FC<AgentLogoProps> = ({ agent }) => {
  if (agent.avatar) {
    return (
      <div className="relative h-8 w-8 flex-shrink-0 group">
        <div className="absolute inset-0 rounded-full bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
        <Image
          src={agent.avatar}
          alt={`${agent.name} avatar`}
          width={32}
          height={32}
          className="relative z-10 rounded-full object-cover shadow-sm ring-1 ring-black/5 dark:ring-white/10 group-hover:shadow-md transition-shadow duration-200"
          onError={(e) => {
            // Fallback to User icon with enhanced styling
            const target = e.target as HTMLImageElement;
            target.style.display = 'none';
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = `
                <div class="h-8 w-8 rounded-full bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-800/30 dark:to-pink-800/30 flex items-center justify-center shadow-sm ring-1 ring-black/5 dark:ring-white/10">
                  <svg class="h-5 w-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
              `;
            }
          }}
        />
      </div>
    );
  }

  return (
    <div className="h-8 w-8 rounded-full bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-800/30 dark:to-pink-800/30 flex items-center justify-center shadow-sm ring-1 ring-black/5 dark:ring-white/10 group-hover:shadow-md transition-shadow duration-200">
      <User className="h-5 w-5 text-purple-600 dark:text-purple-400" />
    </div>
  );
};

interface SidebarProps {
  components: ComponentsApiResponse;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  mcpCategories?: string[];
  loadedMcpCategories?: Set<string>;
  onLoadMCPCategory?: (category: string) => Promise<void>;
  workflowComponents?: WorkflowListItem[];
  isLoadingWorkflows?: boolean;
}

// Helper function to make a node draggable
const onDragStart = (
  event: React.DragEvent,
  nodeType: string,
  definition: ComponentDefinition,
  setShowOutputSchemaDialog: React.Dispatch<React.SetStateAction<boolean>>,
  setSelectedMCPTool: React.Dispatch<
    React.SetStateAction<{ name: string; definition: ComponentDefinition } | null>
  >,
  setShowEnvKeysDialog?: React.Dispatch<React.SetStateAction<boolean>>,
) => {
  // Check if this is an MCP tool and if it has an output schema
  if (definition.type === "MCP" && definition.mcp_info) {
    console.log(
      `Dragging MCP tool: ${definition.display_name}`,
      `Dragging MCP tool: ${definition}`,
      {
        hasOutputSchema: !!definition.mcp_info.output_schema,
        outputSchema: definition.mcp_info.output_schema,
        envKeys: definition.env_keys,
        envCredentialStatus: definition.env_credential_status,
        toolName: definition.mcp_info.tool_name,
      },
    );

    // Special case for MCP server components (tool_name === "server")
    const isMCPServer = definition.mcp_info.tool_name === "server";

    // If output_schema is null or undefined and not an MCP server, prevent drag and show dialog
    if (!definition.mcp_info.output_schema && !isMCPServer) {
      event.preventDefault();
      console.log(`Preventing drag for MCP tool without output schema: ${definition.display_name}`);

      // Set the selected MCP tool for the dialog
      setSelectedMCPTool({
        name: definition.display_name.split(" - ")[1] || definition.display_name,
        definition,
      });
      // Show the output schema dialog
      setShowOutputSchemaDialog(true);
      toast.info(
        `Please define an output schema for ${definition.display_name.split(" - ")[1] || definition.display_name} before adding it to the canvas.`,
      );
      return;
    }

    // If env_keys is not null and has items, and env_credential_status is not "provided",
    // prevent drag and show dialog. This applies to both MCP tools and MCP servers
    if (
      definition.env_keys &&
      definition.env_keys.length > 0 &&
      definition.env_credential_status !== "provided"
    ) {
      event.preventDefault();
      console.log(`Preventing drag for MCP tool with env keys: ${definition.display_name}`);

      // Set the selected MCP tool for the dialog
      setSelectedMCPTool({
        name: definition.display_name.split(" - ")[1] || definition.display_name,
        definition,
      });
      // Show the env keys dialog if the setter is provided
      if (setShowEnvKeysDialog) {
        setShowEnvKeysDialog(true);
      }
      toast.info(
        `Please provide environment keys for ${definition.display_name.split(" - ")[1] || definition.display_name} before adding it to the canvas.`,
      );
      return;
    }
  }

  // If not an MCP tool or has output schema, proceed with drag
  const nodeData = JSON.stringify({ nodeType, definition });
  event.dataTransfer.setData("application/reactflow", nodeData);
  event.dataTransfer.effectAllowed = "move";
};

// Helper function to get appropriate icon based on category
const getCategoryIcon = (category: string) => {
  switch (category.toLowerCase()) {
    case "io":
      return LogIn;
    case "data":
      return Database;
    case "processing":
      return Cpu;
    case "api":
      return ArrowRightLeft;
    case "control flow":
      return Workflow;
    case "text":
      return FileText;
    case "code":
      return Code;
    case "ai":
      return Brain; // AI components use Brain icon
    case "mcp":
      return Cpu; // MCP components use CPU icon
    case "marketplace":
    case "mcp marketplace":
    case "tools":
      return Store; // Tools components use Store icon
    case "agents":
      return Users; // Agents use Users icon
    case "workflows":
      return Workflow; // Workflows use Workflow icon
    case "notifications alerts":
      return Bell; // Notifications & Alerts components use Bell icon
    case "communication":
      return MessageCircle; // Communication components use MessageCircle icon
    case "social media":
      return Share2; // Social Media components use Share2 icon
    case "database":
      return Database; // Database components use Database icon
    case "cloud storage":
      return CloudUpload; // Cloud Storage components use CloudUpload icon
    case "devops system":
      return Settings; // DevOps System components use Settings icon
    case "file handling":
      return FileText; // File Handling components use FileText icon
    default:
      return Cog;
  }
};

// Helper function to get user-friendly category display name
const getCategoryDisplayName = (category: string) => {
  switch (category.toLowerCase()) {
    case "io":
      return "Input/Output";
    case "ai":
      return "AI/LLM";
    case "mcp":
      return "MCP Marketplace";
    case "mcp marketplace":
      return "Tools";
    case "tools":
      return "Tools";
    case "agents":
      return "Agents";
    case "workflows":
      return "Workflows";
    case "notifications alerts":
      return "Notifications & Alerts";
    case "communication":
      return "Communication";
    case "social media":
      return "Social Media";
    case "database":
      return "Database";
    case "cloud storage":
      return "Cloud Storage";
    case "devops system":
      return "DevOps System";
    case "file handling":
      return "File Handling";
    default:
      return category.charAt(0).toUpperCase() + category.slice(1);
  }
};

export const Sidebar = React.memo(function Sidebar({
  components,
  collapsed = false,
  onToggleCollapse,
  mcpCategories = [],
  loadedMcpCategories = new Set(),
  onLoadMCPCategory,
  workflowComponents = [],
  isLoadingWorkflows = false,
}: SidebarProps) {
  // Remove all console.log statements to prevent unnecessary work during renders

  console.log("Sidebar component categories:", Object.keys(components));

  // Check if MCP category exists and log its contents
  if (components.MCP) {
    console.log("MCP category exists with components:", Object.keys(components.MCP));
  } else {
    console.log("MCP category does not exist in components");
  }
  const categories = Object.keys(components).sort();

  const [searchTerm, setSearchTerm] = useState("");
  // Initialize with IO, AI, Data, and MCP categories expanded by default
  const [expandedCategories, setExpandedCategories] = useState<string[]>(
    categories.filter((cat) => {
      const catLower = cat.toLowerCase();
      const isMCPEnumCategory = Object.values(MCP_CATEGORY_LABELS).map(label => label.toLowerCase()).includes(catLower);
      return ["io", "ai", "data", "mcp", "mcp marketplace", "tools", "agents"].includes(catLower) || isMCPEnumCategory;
    }),
  );

  // State to track expanded MCP server groups
  const [expandedMCPGroups, setExpandedMCPGroups] = useState<Record<string, string[]>>({});

  // State for output schema dialog
  const [showOutputSchemaDialog, setShowOutputSchemaDialog] = useState(false);
  const [selectedMCPTool, setSelectedMCPTool] = useState<{
    name: string;
    definition: ComponentDefinition;
  } | null>(null);

  // State for env keys dialog
  const [showEnvKeysDialog, setShowEnvKeysDialog] = useState(false);

  // State for agents
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoadingAgents, setIsLoadingAgents] = useState(false);

  // We'll only load agents when the components prop changes, ensuring everything loads together
  useEffect(() => {
    if (Object.keys(components).length > 0) {
      async function loadAgents() {
        setIsLoadingAgents(true);
        try {
          // Using the dedicated agentApi for better error handling
          const result = await fetchAgents({
            page: 1,
            page_size: 50, // Adjust as needed
          });
          setAgents(result.data);
          console.log("Loaded agents together with components:", result.data);
        } catch (error) {
          console.error("Error loading agents:", error);
          toast.error("Failed to load agents");
        } finally {
          setIsLoadingAgents(false);
        }
      }
      
      // Only load agents after components are available
      loadAgents();
    }
  }, [components]); // Depend on components prop to ensure everything loads together

  // Filter components based on search term
  const filteredComponents = useCallback(() => {
    // Start with a base set of components, but filter out legacy categories that have enum equivalents
    const filtered: ComponentsApiResponse = {};
    
    // First, add all non-MCP categories from components
    Object.entries(components).forEach(([category, categoryComponents]) => {
      // Check if this category has an enum equivalent
      const hasEnumEquivalent = MCP_CATEGORIES.some(enumKey => {
        const enumLabel = MCP_CATEGORY_LABELS[enumKey];
        return enumLabel === category || enumKey === category;
      });
      
      // If it doesn't have an enum equivalent, or if it's the proper enum label, add it
      if (!hasEnumEquivalent || Object.values(MCP_CATEGORY_LABELS).includes(category)) {
        filtered[category] = categoryComponents;
      }
    });
    
    // Add placeholder categories for all MCP categories from enum
    MCP_CATEGORIES.forEach(category => {
      const categoryKey = MCP_CATEGORY_LABELS[category];
      if (!filtered[categoryKey]) {
        filtered[categoryKey] = {};
      }
    });
    
    // Also add any additional mcpCategories passed as props (for backward compatibility)
    // But skip if we already have the enum version
    mcpCategories.forEach(category => {
      const isAlreadyAddedAsEnum = Object.values(MCP_CATEGORY_LABELS).includes(category);
      const hasEnumEquivalent = MCP_CATEGORIES.some(enumKey => {
        const enumLabel = MCP_CATEGORY_LABELS[enumKey];
        return enumLabel === category || enumKey === category;
      });
      
      if (!filtered[category] && !isAlreadyAddedAsEnum && !hasEnumEquivalent) {
        filtered[category] = {};
      }
    });
    
    // Add the Agents category if we have agents
    if (agents.length > 0) {
      filtered["Agents"] = {};
    }
    
    // Add the Workflows category if we have workflow components
    if (workflowComponents.length > 0) {
      filtered["Workflows"] = {};
    }
    
    // If we're searching, we need to filter components and agents
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      
      // Create a new filtered object
      const searchFiltered: ComponentsApiResponse = {};
      
      // Filter regular components, but skip legacy categories that have enum equivalents
      Object.entries(components).forEach(([category, categoryComponents]) => {
        // Check if this category has an enum equivalent
        const hasEnumEquivalent = MCP_CATEGORIES.some(enumKey => {
          const enumLabel = MCP_CATEGORY_LABELS[enumKey];
          return enumLabel === category || enumKey === category;
        });
        
        // If it has an enum equivalent and it's not the proper enum label, skip it
        if (hasEnumEquivalent && !Object.values(MCP_CATEGORY_LABELS).includes(category)) {
          return;
        }
        
        const matchingComponents = Object.values(categoryComponents).filter(
          (comp) =>
            comp.display_name.toLowerCase().includes(searchLower) ||
            comp.description.toLowerCase().includes(searchLower),
        );

        if (matchingComponents.length > 0) {
          searchFiltered[category] = Object.fromEntries(
            matchingComponents.map((comp) => [comp.name, comp]),
          );
        }
      });
      
      // Add MCP categories that match the search (even if not loaded yet)
      MCP_CATEGORIES.forEach(category => {
        const categoryKey = MCP_CATEGORY_LABELS[category];
        if (categoryKey.toLowerCase().includes(searchLower) || category.toLowerCase().includes(searchLower)) {
          if (!searchFiltered[categoryKey]) {
            searchFiltered[categoryKey] = components[categoryKey] || {};
          }
        }
      });
      
      // Also check additional mcpCategories passed as props (for backward compatibility)
      // But skip if we already have the enum version
      mcpCategories.forEach(category => {
        const isAlreadyAddedAsEnum = Object.values(MCP_CATEGORY_LABELS).includes(category);
        const hasEnumEquivalent = MCP_CATEGORIES.some(enumKey => {
          const enumLabel = MCP_CATEGORY_LABELS[enumKey];
          return enumLabel === category || enumKey === category;
        });
        
        if (category.toLowerCase().includes(searchLower) && !isAlreadyAddedAsEnum && !hasEnumEquivalent) {
          if (!searchFiltered[category]) {
            searchFiltered[category] = components[category] || {};
          }
        }
      });
      
      // Filter agents and add them to a dedicated category only if there are matches
      const filteredAgents = agents.filter(
        (agent) =>
          agent.name.toLowerCase().includes(searchLower) ||
          agent.description.toLowerCase().includes(searchLower) ||
          (agent.category && agent.category.toLowerCase().includes(searchLower)),
      );

      // Only add agents category if there are matching agents
      if (filteredAgents.length > 0) {
        searchFiltered["Agents"] = {};
      }
      
      // Filter workflow components and add them to a dedicated category only if there are matches
      const filteredWorkflows = workflowComponents.filter(
        (workflow) =>
          workflow.name.toLowerCase().includes(searchLower) ||
          (workflow.description && workflow.description.toLowerCase().includes(searchLower)),
      );

      // Only add workflows category if there are matching workflows
      if (filteredWorkflows.length > 0) {
        searchFiltered["Workflows"] = {};
      }
      
      return searchFiltered;
    }
    
    return filtered;
  }, [components, searchTerm, agents, mcpCategories, workflowComponents]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    // Auto-expand all categories when searching
    if (value.trim()) {
      const filteredCats = Object.keys(filteredComponents());
      setExpandedCategories(filteredCats);

      // Also expand all MCP groups in the filtered categories
      const newExpandedGroups: Record<string, string[]> = {};
      filteredCats.forEach((category) => {
        const categoryComponents = filteredComponents()[category];
        if (categoryComponents) {
          // Find all MCP groups in this category
          const mcpGroups = new Set<string>();
          Object.values(categoryComponents).forEach((comp) => {
            if (comp.type === "MCP" && comp.display_name.includes(" - ")) {
              const mcpName = comp.display_name.split(" - ")[0];
              mcpGroups.add(mcpName);
            }
          });

          if (mcpGroups.size > 0) {
            newExpandedGroups[category] = Array.from(mcpGroups);
          }
        }
      });

      setExpandedMCPGroups(newExpandedGroups);
    }
  };

  // Toggle category expansion
  const handleCategoryToggle = async (category: string) => {
    const isExpanding = !expandedCategories.includes(category);
    
    // Update expanded categories state
    setExpandedCategories((prev) =>
      prev.includes(category) ? prev.filter((c) => c !== category) : [...prev, category],
    );
    
    // Check if this is an MCP category from our enum
    const isMCPEnumCategory = Object.values(MCP_CATEGORY_LABELS).includes(category);
    
    // If expanding an MCP category that hasn't been loaded yet, load it
    // For enum categories, we need to find the corresponding enum key
    let categoryToLoad = category;
    if (isMCPEnumCategory) {
      // Find the enum key that corresponds to this label
      const enumKey = Object.keys(MCP_CATEGORY_LABELS).find(
        key => MCP_CATEGORY_LABELS[key as MCPCategory] === category
      );
      if (enumKey) {
        categoryToLoad = enumKey;
      }
    }
    
    if (isExpanding && (mcpCategories.includes(category) || isMCPEnumCategory) && !loadedMcpCategories.has(categoryToLoad) && onLoadMCPCategory) {
      try {
        await onLoadMCPCategory(categoryToLoad);
      } catch (error) {
        console.error(`Failed to load MCP category ${categoryToLoad}:`, error);
        // Optionally show a toast error message
      }
    }
  };

  // Toggle MCP group expansion
  const handleMCPGroupToggle = (category: string, mcpName: string) => {
    setExpandedMCPGroups((prev) => {
      const categoryGroups = prev[category] || [];
      const newCategoryGroups = categoryGroups.includes(mcpName)
        ? categoryGroups.filter((name) => name !== mcpName)
        : [...categoryGroups, mcpName];

      return {
        ...prev,
        [category]: newCategoryGroups,
      };
    });
  };

  // Memoize the filtered data to prevent recalculation on every render
  const filteredData = useMemo(() => filteredComponents(), [filteredComponents]);

  // Memoize the visible categories to prevent recalculation on every render
  const visibleCategories = useMemo(() => {
    // Get categories from filtered data
    let categories = Object.keys(filteredData);
    
    // Filter out the "io" category and then sort the remaining categories
    return categories
      .filter((category) => category.toLowerCase() !== "io") // Comment out IO category
      .sort((a, b) => {
        // Priority order: AI first, then MCP Marketplace, then Agents, then Data, then new MCP categories, then alphabetical
        // IO category is filtered out
        const aLower = a.toLowerCase();
        const bLower = b.toLowerCase();

        if (aLower === "ai") return -1;
        if (bLower === "ai") return 1;
        if (aLower === "mcp marketplace" || aLower === "tools") return -1;
        if (bLower === "mcp marketplace" || bLower === "tools") return 1;
        if (aLower === "agents") return -1;
        if (bLower === "agents") return 1;
        if (aLower === "workflows") return -1;
        if (bLower === "workflows") return 1;
        if (aLower === "mcp") return -1;
        if (bLower === "mcp") return 1;
        if (aLower === "data") return -1;
        if (bLower === "data") return 1;

        // Group MCP categories together after the priority categories
        const mcpCategoryLabels = Object.values(MCP_CATEGORY_LABELS).map(label => label.toLowerCase());
        const aIsMcp = mcpCategoryLabels.includes(aLower);
        const bIsMcp = mcpCategoryLabels.includes(bLower);

        if (aIsMcp && !bIsMcp) return -1;
        if (!aIsMcp && bIsMcp) return 1;
        if (aIsMcp && bIsMcp) {
          // Sort MCP categories in a specific order
          return mcpCategories.indexOf(aLower) - mcpCategories.indexOf(bLower);
        }

        return a.localeCompare(b);
      });
  }, [filteredData, agents, workflowComponents]);

  // Handle output schema submission
  const handleOutputSchemaSubmit = async (schema: object) => {
    if (selectedMCPTool && selectedMCPTool.definition.mcp_info) {
      try {
        // Show loading toast with an ID so we can dismiss it later
        const toastId = toast.loading(`Saving output schema for ${selectedMCPTool.name}...`);

        // Get the MCP server ID and tool name from the definition
        const mcpInfo = selectedMCPTool.definition.mcp_info;
        const mcpId = mcpInfo.server_id;
        const toolName = mcpInfo.tool_name;

        // Import the API function
        const { updateMCPToolOutputSchema } = await import("@/lib/api");

        // Call the API to update the output schema on the server
        const result = await updateMCPToolOutputSchema({
          mcp_id: mcpId,
          tool_name: toolName,
          output_schema_json: schema,
        });

        // Dismiss the loading toast
        toast.dismiss(toastId);

        if (result.success) {
          // Update the output schema in the definition
          selectedMCPTool.definition.mcp_info.output_schema = schema;

          // Show success message
          toast.success(`Output schema added for ${selectedMCPTool.name}`);

          // Reset state
          setSelectedMCPTool(null);
          setShowOutputSchemaDialog(false);
        } else {
          // Show error message
          toast.error(`Failed to save output schema: ${result.error || "Unknown error"}`);
        }
      } catch (error) {
        console.error("Error saving output schema:", error);
        // Dismiss any loading toasts that might be active
        toast.dismiss();
        toast.error(
          `Error saving output schema: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
      }
    }
  };

  // Handle environment keys submission
  const handleEnvKeysSubmit = async (envKeyValues: Array<{ key: string; value: string }>) => {
    if (selectedMCPTool && selectedMCPTool.definition.mcp_info) {
      try {
        // Show loading toast with an ID so we can dismiss it later
        const toastId = toast.loading(`Saving environment keys for ${selectedMCPTool.name}...`);

        // Get the MCP server ID from the definition
        const mcpInfo = selectedMCPTool.definition.mcp_info;
        const mcpId = mcpInfo.server_id;

        // Import the API function
        const { updateMCPEnvironmentKeys } = await import("@/lib/api");

        // Call the API to update the environment keys on the server
        const result = await updateMCPEnvironmentKeys({
          mcp_id: mcpId,
          env_key_values: envKeyValues,
        });

        // Dismiss the loading toast
        toast.dismiss(toastId);

        if (result.success) {
          // Show success message
          toast.success(`Environment keys added for ${selectedMCPTool.name}`);

          // Reset state
          setSelectedMCPTool(null);
          setShowEnvKeysDialog(false);
        } else {
          // Show error message
          toast.error(`Failed to save environment keys: ${result.error || "Unknown error"}`);
        }
      } catch (error) {
        console.error("Error saving environment keys:", error);
        // Dismiss any loading toasts that might be active
        toast.dismiss();
        toast.error(
          `Error saving environment keys: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
      }
    }
  };

  return (
    <aside
      className={`bg-sidebar border-brand-stroke relative flex h-full shrink-0 flex-col overflow-hidden border-r shadow-md transition-all duration-300 ${
        collapsed ? "w-16" : "w-80"
      }`}
    >
      {/* Theme-responsive overlay */}
      <div className="pointer-events-none absolute inset-0 z-0 bg-black/5 dark:bg-black/20"></div>

      {/* Removed the border button */}

      <div
        className={`relative z-10 flex-shrink-0 bg-[white] dark:bg-black ${collapsed ? "p-3" : "p-5"}`}
      >
        {!collapsed && (
          <div className="relative flex items-center">
            <div className="relative flex-grow">
              <Search className="text-brand-secondary absolute top-3 left-3 h-5 w-5" />
              <Input
                placeholder="Search components..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="border-brand-stroke text-brand-primary-font placeholder:text-brand-secondary-font focus-visible:ring-brand-primary/30 dark:text-brand-white-text dark:placeholder:text-brand-secondary-font h-11 rounded-md bg-white/90 pl-10 text-sm dark:border-[#3F3F46] dark:bg-[#18181B]"
              />
            </div>
            <button
              onClick={onToggleCollapse}
              className="text-brand-primary hover:text-brand-primary dark:text-brand-secondary dark:hover:text-brand-secondary ml-2 flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-white/90 shadow-sm transition-all hover:bg-[#F9F9F9] hover:shadow-md dark:hover:bg-[#212121]"
              aria-label="Collapse sidebar"
              title="Collapse sidebar (Alt+S)"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M15 6L9 12L15 18"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        )}
        {collapsed && (
          <div className="flex items-center justify-center">
            <button
              onClick={onToggleCollapse}
              className="text-brand-primary hover:text-brand-primary dark:text-brand-secondary dark:hover:text-brand-secondary flex h-9 w-9 flex-shrink-0 items-center justify-center rounded-full bg-white/90 shadow-sm transition-all hover:bg-[#F9F9F9] hover:shadow-md dark:hover:bg-[#212121]"
              aria-label="Expand sidebar"
              title="Expand sidebar (Alt+S)"
            >
              {/* Hamburger menu icon */}
              <svg
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3 12H21M3 6H21M3 18H21"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        )}
      </div>

      <CustomScrollArea className="custom-scrollbar relative z-10 flex-grow bg-[#FEFEFE] dark:bg-black">
        {!collapsed ? (
          <>
            <Accordion
              type="multiple"
              className="w-full space-y-3 px-5 py-4"
              value={expandedCategories}
            >
              {visibleCategories.map((category) => {
                const CategoryIcon = getCategoryIcon(category);
                return (
                  <AccordionItem
                    value={category}
                    key={category}
                    className="overflow-hidden rounded-lg bg-white shadow-md hover:bg-[#F9F9F9] dark:bg-[#1E1E1E] dark:hover:bg-[#212121]"
                  >
                    <AccordionTrigger
                      className="font-primary px-4 py-4 text-base font-semibold transition-all duration-200 hover:bg-[#F9F9F9] hover:no-underline dark:hover:bg-[#212121]"
                      onClick={() => handleCategoryToggle(category)}
                    >
                      <div className="flex w-full items-center gap-2">
                        <CategoryIcon className="text-brand-primary dark:text-brand-secondary h-6 w-6" />
                        <span className="text-brand-primary-font dark:text-brand-white-text">
                          {getCategoryDisplayName(category)}
                        </span>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="accordion-content-animation px-3 pb-4">
                      <div className="max-h-[300px] space-y-4 overflow-y-auto pt-3 pr-2">
                        {(() => {
                            // Check if this is an MCP category from our enum
                            const isEnumMCPCategory = Object.values(MCP_CATEGORY_LABELS).includes(category);
                            
                            // For enum categories, we need to check the enum key, not the display label
                            let categoryToCheck = category;
                            if (isEnumMCPCategory) {
                              // Find the enum key that corresponds to this label
                              const enumKey = Object.keys(MCP_CATEGORY_LABELS).find(
                                key => MCP_CATEGORY_LABELS[key as MCPCategory] === category
                              );
                              if (enumKey) {
                                categoryToCheck = enumKey;
                              }
                            }
                            
                            // Handle loading state for MCP categories with skeleton view
                            if ((mcpCategories.includes(category) || isEnumMCPCategory) && !loadedMcpCategories.has(categoryToCheck)) {
                              return (
                                <div className="space-y-4">
                                  {/* Skeleton for MCP group header */}
                                  <div className="mb-4">
                                    <div className="flex items-center gap-2.5 p-2 border-b border-gray-200/60 dark:border-gray-700/60 pb-2">
                                      <div className="h-8 w-8 rounded-md bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                                      <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                                    </div>
                                    
                                    {/* Skeleton for MCP components */}
                                    <div className="space-y-3 pl-2 mt-3">
                                      {[1, 2, 3].map((index) => (
                                        <div
                                          key={index}
                                          className="rounded-md bg-white/90 dark:bg-[#1B1B1B] p-4 space-y-2"
                                        >
                                          <div className="flex items-center gap-2">
                                            <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                                          </div>
                                          <div className="space-y-1">
                                            <div className="h-3 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                                            <div className="h-3 w-3/4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                  
                                  {/* Additional skeleton groups */}
                                  <div className="mb-4">
                                    <div className="flex items-center gap-2.5 p-2 border-b border-gray-200/60 dark:border-gray-700/60 pb-2">
                                      <div className="h-8 w-8 rounded-md bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                                      <div className="h-4 w-28 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                                    </div>
                                    
                                    <div className="space-y-3 pl-2 mt-3">
                                      {[1, 2].map((index) => (
                                        <div
                                          key={index}
                                          className="rounded-md bg-white/90 dark:bg-[#1B1B1B] p-4 space-y-2"
                                        >
                                          <div className="flex items-center gap-2">
                                            <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                                          </div>
                                          <div className="space-y-1">
                                            <div className="h-3 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                                            <div className="h-3 w-2/3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              );
                            }
                            
                            // Handle special case for Agents category
                            if (category === "Agents") {
                            return (
                              <div className="space-y-3">
                                {isLoadingAgents ? (
                                  <div className="flex justify-center p-4">
                                    <div className="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full"></div>
                                  </div>
                                ) : agents.length === 0 ? (
                                  <div className="text-center p-4 text-sm text-gray-500 dark:text-gray-400">
                                    No agents found
                                  </div>
                                ) : (
                                  agents
                                    .filter(agent => {
                                      // Apply search filter if there's a search term
                                      if (!searchTerm) return true;
                                      const searchLower = searchTerm.toLowerCase();
                                      return (
                                        agent.name.toLowerCase().includes(searchLower) ||
                                        agent.description.toLowerCase().includes(searchLower) ||
                                        (agent.category && agent.category.toLowerCase().includes(searchLower))
                                      );
                                    })
                                    .map((agent) => (
                                      <div
                                        key={agent.id}
                                        className="group relative cursor-grab rounded-md bg-white/90 p-4 text-sm transition-all duration-200 hover:translate-y-[-2px] hover:bg-[#F9F9F9] hover:shadow-lg dark:bg-[#1B1B1B] dark:hover:bg-[#212121]"
                                        onDragStart={(event) =>
                                          onDragStart(
                                            event,
                                            `agent-${agent.id}`,
                                            {
                                              name: `agent-${agent.id}`,
                                              display_name: agent.name,
                                              description: agent.description,
                                              category: "Agents",
                                              icon: "Users",
                                              beta: false,
                                              path: `agent.${agent.id}`,
                                              inputs: [
                                                {
                                                  name: "input",
                                                  display_name: "Input",
                                                  info: "Input message to send to the agent",
                                                  input_type: "string",
                                                  required: true,
                                                  is_handle: true
                                                }
                                              ],
                                              outputs: [
                                                {
                                                  name: "response",
                                                  display_name: "Response",
                                                  output_type: "string"
                                                }
                                              ],
                                              is_valid: true,
                                              type: "Agent",
                                              agent_info: agent, // Store the full agent data
                                            },
                                            setShowOutputSchemaDialog,
                                            setSelectedMCPTool,
                                            setShowEnvKeysDialog,
                                          )
                                        }
                                        draggable
                                      >
                                        <div className="absolute top-1 right-1 opacity-40 transition-opacity group-hover:opacity-80">
                                          <Grip className="text-brand-secondary/70 dark:text-brand-secondary/80 h-4 w-4" />
                                        </div>
                                        <div className="font-primary mb-2 flex items-center gap-2 font-medium">
                                          <AgentLogo agent={agent} />
                                          <span className="text-sm text-black transition-colors group-hover:text-black/80 dark:text-white dark:group-hover:text-white/80">
                                            {agent.name}
                                          </span>
                                        </div>
                                        <p className="font-secondary text-brand-secondary-font dark:text-brand-secondary-font line-clamp-2 text-xs leading-tight">
                                          {agent.description}
                                        </p>
                                      </div>
                                    ))
                                )}
                              </div>
                            );
                          }

                          // Handle special case for Workflows category
                          if (category === "Workflows") {
                            return (
                              <div className="space-y-3">
                                {isLoadingWorkflows ? (
                                  <div className="flex justify-center p-4">
                                    <div className="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full"></div>
                                  </div>
                                ) : workflowComponents.length === 0 ? (
                                  <div className="text-center p-4 text-sm text-gray-500 dark:text-gray-400">
                                    No workflows found
                                  </div>
                                ) : (
                                  workflowComponents
                                    .filter(workflow => {
                                      // Apply search filter if there's a search term
                                      if (!searchTerm) return true;
                                      const searchLower = searchTerm.toLowerCase();
                                      return (
                                        workflow.name.toLowerCase().includes(searchLower) ||
                                        (workflow.description && workflow.description.toLowerCase().includes(searchLower))
                                      );
                                    })
                                    .map((workflow) => (
                                      <div
                                        key={workflow.id}
                                        className="group relative cursor-grab rounded-md bg-white/90 p-4 text-sm transition-all duration-200 hover:translate-y-[-2px] hover:bg-[#F9F9F9] hover:shadow-lg dark:bg-[#1B1B1B] dark:hover:bg-[#212121]"
                                        onDragStart={(event) =>
                                          onDragStart(
                                            event,
                                            `workflow-${workflow.id}`,
                                            {
                                              name: `workflow-${workflow.id}`,
                                              display_name: workflow.name,
                                              description: workflow.description,
                                              category: "Workflows",
                                              icon: "Workflow",
                                              beta: false,
                                              path: `workflow.${workflow.id}`,
                                              inputs: generateWorkflowInputs(workflow.start_nodes || []),
                                              outputs: [
                                                {
                                                  name: "result",
                                                  display_name: "Result",
                                                  output_type: "array"
                                                }
                                              ],
                                              is_valid: true,
                                              type: "Workflow",
                                              workflow_info: workflow, // Store the full workflow data
                                            },
                                            setShowOutputSchemaDialog,
                                            setSelectedMCPTool,
                                            setShowEnvKeysDialog,
                                          )
                                        }
                                        draggable
                                      >
                                        <div className="absolute top-1 right-1 opacity-40 transition-opacity group-hover:opacity-80">
                                          <Grip className="text-brand-secondary/70 dark:text-brand-secondary/80 h-4 w-4" />
                                        </div>
                                        <div className="font-primary mb-2 flex items-center gap-2 font-medium">
                                          <div className="h-8 w-8 rounded-md bg-gradient-to-br from-indigo-100 to-purple-100 dark:from-indigo-800/30 dark:to-purple-800/30 flex items-center justify-center shadow-sm ring-1 ring-black/5 dark:ring-white/10 group-hover:shadow-md transition-shadow duration-200">
                                            <Workflow className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                                          </div>
                                          <span className="text-sm text-black transition-colors group-hover:text-black/80 dark:text-white dark:group-hover:text-white/80">
                                            {workflow.name}
                                          </span>
                                        </div>
                                        <p className="font-secondary text-brand-secondary-font dark:text-brand-secondary-font line-clamp-2 text-xs leading-tight">
                                          {workflow.description}
                                        </p>
                                      </div>
                                    ))
                                )}
                              </div>
                            );
                          }
                          
                          // Check if this is an MCP category from our enum and show "No MCP Added" message if empty
                          const isMCPEnumCategory = Object.values(MCP_CATEGORY_LABELS).includes(category);
                          if (isMCPEnumCategory) {
                            const components = Object.values(filteredData[category] || {});
                            if (components.length === 0) {
                              return (
                                <div className="text-center p-6 text-sm text-gray-500 dark:text-gray-400">
                                  <div className="mb-3">
                                    <Store className="h-8 w-8 mx-auto text-gray-400 dark:text-gray-500" />
                                  </div>
                                  <p className="font-medium mb-1">No MCP Added</p>
                                  <p className="text-xs mb-4">
                                    Go to marketplace and add MCP in this {category.toLowerCase()}
                                  </p>
                                  <button
                                    onClick={() => window.open('https://ruh-marketplace.rapidinnovation.dev/', '_blank')}
                                    className="inline-flex items-center gap-2 px-4 py-2 text-xs font-medium text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-800 dark:hover:text-gray-100 transition-colors duration-200"
                                  >
                                    <Search className="h-4 w-4" />
                                    Explore more
                                  </button>
                                </div>
                              );
                            }
                          }
                          
                          // Default handling for regular component categories
                          // Group components by MCP server name
                          const groupedByMCP: Record<string, ComponentDefinition[]> = {};

                          // Get all components for this category
                          const components = Object.values(filteredData[category] || {}).sort((a, b) =>
                            a.display_name.localeCompare(b.display_name),
                          );
                          console.log("components", components);

                          // Group components by MCP server name (first part of display_name before " - ")
                          components.forEach((compDef) => {
                            // Check if this is an MCP component
                            const isMCPComponent = compDef.type === "MCP";

                            // Check if this is an MCP tool (has display_name in format "MCPName - ToolName")
                            const isMCPTool =
                              isMCPComponent && compDef.display_name.includes(" - ");

                            // Check if this is an MCP server (has mcp_info.tool_name === "server")
                            const isMCPServer =
                              isMCPComponent &&
                              compDef.mcp_info &&
                              compDef.mcp_info.tool_name === "server";

                            if (isMCPTool) {
                              // Extract MCP name from display_name (format: "MCPName - ToolName")
                              const mcpName = compDef.display_name.split(" - ")[0];
                              if (!groupedByMCP[mcpName]) {
                                groupedByMCP[mcpName] = [];
                              }
                              groupedByMCP[mcpName].push(compDef);
                            } else if (isMCPServer) {
                              // For MCP server components, use the display_name as the mcpName
                              const mcpName = compDef.display_name;
                              if (!groupedByMCP[mcpName]) {
                                groupedByMCP[mcpName] = [];
                              }
                              groupedByMCP[mcpName].push(compDef);
                            } else {
                              // For non-MCP components, use a special key
                              if (!groupedByMCP["__other"]) {
                                groupedByMCP["__other"] = [];
                              }
                              groupedByMCP["__other"].push(compDef);
                            }
                          });

                          // Initialize expanded MCP groups for this category if not already set
                          if (searchTerm.trim() && !expandedMCPGroups[category]) {
                            // Auto-expand all MCP groups when searching
                            const mcpNames = Object.keys(groupedByMCP).filter(
                              (name) => name !== "__other",
                            );
                            if (mcpNames.length > 0) {
                              setExpandedMCPGroups((prev) => ({
                                ...prev,
                                [category]: mcpNames,
                              }));
                            }
                          }

                          // Get the list of expanded MCP groups for this category
                          const expandedGroups = expandedMCPGroups[category] || [];

                          return (
                            <>
                              {/* Render MCP groups first */}
                              {Object.entries(groupedByMCP)
                                .filter(([mcpName]) => mcpName !== "__other")
                                .map(([mcpName, mcpComponents]) => (
                                  <div key={mcpName} className="mb-4">
                                    {/* MCP Server Name as collapsible header */}
                                    <div
                                      className="text-brand-primary-font dark:text-brand-white-text mb-3 p-2 flex cursor-pointer items-center justify-between border-b border-gray-200/60 pb-2 text-sm font-medium dark:border-gray-700/60 hover:border-gray-300 dark:hover:border-gray-600 transition-colors duration-200 group"
                                      onClick={() => handleMCPGroupToggle(category, mcpName)}
                                    >
                                      <div className="flex items-center gap-2.5">
                                        <MCPLogo mcpName={mcpName} mcpComponents={mcpComponents} />
                                        <span className="group-hover:text-brand-primary dark:group-hover:text-brand-secondary transition-colors duration-200">{mcpName}</span>
                                      </div>
                                      <div className="p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200">
                                        <svg
                                          width="12"
                                          height="12"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                          className={`text-gray-500 dark:text-gray-400 group-hover:text-brand-primary dark:group-hover:text-brand-secondary transition-all duration-200 ${
                                            expandedGroups.includes(mcpName) ? "rotate-180" : ""
                                          }`}
                                        >
                                          <path
                                            d="M6 9l6 6 6-6"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                          />
                                        </svg>
                                      </div>
                                    </div>

                                    {/* Components from this MCP - only show if expanded */}
                                    {expandedGroups.includes(mcpName) && (
                                      <div className="space-y-3 pl-2">
                                        {mcpComponents.map((compDef: ComponentDefinition) => (
                                          <div
                                            key={compDef.name}
                                            className="group relative cursor-grab rounded-md bg-white/90 p-4 text-sm transition-all duration-200 hover:translate-y-[-2px] hover:bg-[#F9F9F9] hover:shadow-lg dark:bg-[#1B1B1B] dark:hover:bg-[#212121]"
                                            onDragStart={(event) =>
                                              onDragStart(
                                                event,
                                                compDef.name,
                                                compDef,
                                                setShowOutputSchemaDialog,
                                                setSelectedMCPTool,
                                                setShowEnvKeysDialog,
                                              )
                                            }
                                            draggable
                                          >
                                            <div className="absolute top-1 right-1 opacity-40 transition-opacity group-hover:opacity-80">
                                              <Grip className="text-brand-secondary/70 dark:text-brand-secondary/80 h-4 w-4" />
                                            </div>
                                            <div className="font-primary mb-2 flex items-center gap-2 font-medium">
                                              <span className="text-sm text-black transition-colors group-hover:text-black/80 dark:text-white dark:group-hover:text-white/80">
                                                {/* Show only the tool name, not the full "MCPName - ToolName" */}
                                                {compDef.display_name.split(" - ")[1] ||
                                                  compDef.display_name}
                                              </span>
                                              {compDef.type === "MCPMarketplaceComponent" && (
                                                <Badge className="ml-1 bg-blue-500 text-[10px] dark:bg-blue-600">
                                                  MCP
                                                </Badge>
                                              )}
                                            </div>
                                            <div className="mb-2 pt-2"></div>
                                            <p className="font-secondary text-brand-secondary-font dark:text-brand-secondary-font line-clamp-2 text-xs leading-tight">
                                              {compDef.description}
                                            </p>
                                          </div>
                                        ))}
                                      </div>
                                    )}
                                  </div>
                                ))}

                              {/* Render other components */}
                              {groupedByMCP["__other"] && groupedByMCP["__other"].length > 0 && (
                                <div className="space-y-3">
                                  {groupedByMCP["__other"].map((compDef: ComponentDefinition) => (
                                    <div
                                      key={compDef.name}
                                      className="group relative cursor-grab rounded-md bg-white/90 p-4 text-sm transition-all duration-200 hover:translate-y-[-2px] hover:bg-[#F9F9F9] hover:shadow-lg dark:bg-[#1B1B1B] dark:hover:bg-[#212121]"
                                      onDragStart={(event) =>
                                        onDragStart(
                                          event,
                                          compDef.name,
                                          compDef,
                                          setShowOutputSchemaDialog,
                                          setSelectedMCPTool,
                                          setShowEnvKeysDialog,
                                        )
                                      }
                                      draggable
                                    >
                                      <div className="absolute top-1 right-1 opacity-40 transition-opacity group-hover:opacity-80">
                                        <Grip className="text-brand-secondary/70 dark:text-brand-secondary/80 h-4 w-4" />
                                      </div>
                                      <div className="font-primary mb-2 flex items-center gap-2 font-medium">
                                        <span className="text-sm text-black transition-colors group-hover:text-black/80 dark:text-white dark:group-hover:text-white/80">
                                          {compDef.display_name}
                                        </span>
                                        {compDef.type === "MCPMarketplaceComponent" && (
                                          <Badge className="ml-1 bg-blue-500 text-[10px] dark:bg-blue-600">
                                            MCP
                                          </Badge>
                                        )}
                                      </div>
                                      <div className="mb-2 pt-2"></div>
                                      <p className="font-secondary text-brand-secondary-font dark:text-brand-secondary-font line-clamp-2 text-xs leading-tight">
                                        {compDef.description}
                                      </p>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </>
                          );
                        })()}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>

            {visibleCategories.length === 0 && (
              <div className="border-brand-stroke bg-brand-card-hover text-brand-secondary-font dark:border-brand-stroke dark:bg-brand-card dark:text-brand-secondary-font m-4 rounded-lg border p-6 text-center text-sm">
                <Search className="text-brand-primary/70 dark:text-brand-secondary/70 mx-auto mb-3 h-6 w-6" />
                No components match your search.
              </div>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center gap-3 px-1 py-6">
            {visibleCategories.map((category) => {
              const CategoryIcon = getCategoryIcon(category);
              return (
                <div
                  key={category}
                  className="group mb-1 flex flex-col items-center"
                  title={getCategoryDisplayName(category)}
                >
                  <button
                    className={`flex h-10 w-10 items-center justify-center rounded-full shadow-sm ${
                      category.toLowerCase() === "io" || category.toLowerCase() === "data"
                        ? "bg-brand-primary/10 text-brand-primary dark:bg-brand-primary/20 dark:text-brand-secondary"
                        : "bg-brand-card-hover text-brand-primary-font dark:bg-brand-card dark:text-brand-white-text"
                    } transition-all hover:scale-110 hover:shadow-md`}
                    onClick={async () => {
                      if (onToggleCollapse) onToggleCollapse();
                      setTimeout(async () => await handleCategoryToggle(category), 300);
                    }}
                    aria-label={`Open ${getCategoryDisplayName(category)} category`}
                  >
                    <CategoryIcon className="h-5 w-5" />
                  </button>
                  <span className="text-brand-secondary-font mt-1 text-[10px] font-medium opacity-80">
                    {getCategoryDisplayName(category).substring(0, 3)}
                  </span>
                </div>
              );
            })}
          </div>
        )}
      </CustomScrollArea>

      {/* Output Schema Dialog */}
      {selectedMCPTool && (
        <AddOutputSchemaDialog
          open={showOutputSchemaDialog}
          onOpenChange={setShowOutputSchemaDialog}
          toolName={selectedMCPTool.name}
          onSubmit={handleOutputSchemaSubmit}
        />
      )}

      {/* Environment Keys Dialog */}
      {selectedMCPTool && selectedMCPTool.definition.env_keys && (
        <AddEnvKeysDialog
          open={showEnvKeysDialog}
          onOpenChange={setShowEnvKeysDialog}
          toolName={selectedMCPTool.name}
          envKeys={selectedMCPTool.definition.env_keys}
          onSubmit={handleEnvKeysSubmit}
        />
      )}
    </aside>
  );
});
