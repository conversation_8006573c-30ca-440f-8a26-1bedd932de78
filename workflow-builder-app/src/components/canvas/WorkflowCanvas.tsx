
import React, { useState, useC<PERSON>back, useRef, useEffect } from "react";
import React<PERSON>low, {
  ReactFlowProvider,
  addEdge,
  applyNodeChanges,
  applyEdgeChanges,
  Node,
  Edge,
  OnNodesChange,
  OnEdgesChange,
  OnConnect,
  NodeChange,
  EdgeChange,
  Connection,
  Background,
  Controls,
  MiniMap,
  useReactFlow,
  NodeOrigin,
  BackgroundVariant,
  Panel,
} from "reactflow";
import { Trash2, Copy, Clipboard } from "lucide-react";

import { WorkflowNodeData, ComponentDefinition } from "@/types";
import { InspectorPanel } from "@/components/inspector";
import WorkflowNode from "@/components/nodes/WorkflowNode";
import DeletableEdge from "@/components/edges/DeletableEdge";
import { getConnectedNodesWithToolConnections } from "@/lib/validation/toolConnectionFlow";
import { isToolHandle } from "@/utils/toolConnectionUtils";
import { useEdgeVisibility } from "@/hooks/useEdgeVisibility";
import { populateDefaultValues } from "@/utils/inputValueUtils";

// Style for minimap with more modern colors
const nodeColor = (node: Node) => {
  const data = node.data as WorkflowNodeData;
  const category = data?.definition?.category?.toLowerCase() || "";

  // Color nodes based on their category
  switch (category) {
    case "io":
    case "input/output": // Add alias for consistency
      return "var(--chart-1)";
    case "data":
      return "var(--chart-2)";
    case "processing":
      return "var(--chart-3)";
    case "api":
      return "var(--chart-4)";
    case "logic": // Add category for LoopNode
    case "control flow":
      return "var(--chart-5)";
    default:
      return "var(--primary)";
  }
};

// Create a StartNode as the initial node
const createStartNode = (): Node<WorkflowNodeData> => {
  return {
    id: "start-node",
    type: "WorkflowNode",
    position: { x: 100, y: 100 },
    data: {
      label: "Start",
      type: "component",
      originalType: "StartNode",
      definition: {
        name: "StartNode",
        display_name: "Start",
        description:
          "The starting point for all workflows. Only nodes connected to this node will be executed.",
        category: "Input/Output",
        icon: "Play",
        beta: false,
        inputs: [],
        outputs: [
          {
            name: "flow",
            display_name: "Flow",
            output_type: "Any",
          },
        ],
        is_valid: true,
        path: "components.io.start_node",
      },
      config: {
        collected_parameters: {},
      },
    },
  };
};

const initialNodes: Node<WorkflowNodeData>[] = [createStartNode()];
const initialEdges: Edge[] = [];

// Set node origin to top-left for more predictable drag/drop placement
const nodeOrigin: NodeOrigin = [0, 0];

// Define nodeTypes outside the component to prevent re-creation on each render
const nodeTypes = {
  WorkflowNode: WorkflowNode, // Register custom node with a key
};

// Define edgeTypes outside the component to prevent re-creation on each render
const edgeTypes = {
  default: DeletableEdge,
};

interface ApplicationData {
  providers: any[];
  models: any[];
  credentials: any[];
  isLoadingAppData: boolean;
  appDataError: string | null;
}

interface FlowCanvasProps {
  onFlowChange: (nodes: Node<WorkflowNodeData>[], edges: Edge[]) => void;
  initialNodes?: Node<WorkflowNodeData>[];
  initialEdges?: Edge[];
  applicationData?: ApplicationData;
}

function FlowCanvas({
  onFlowChange,
  initialNodes: propInitialNodes,
  initialEdges: propInitialEdges,
  applicationData,
}: FlowCanvasProps) {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { screenToFlowPosition, fitView } = useReactFlow();
  const [isReactFlowReady, setIsReactFlowReady] = useState(false);

  const [nodes, setNodes] = useState<Node<WorkflowNodeData>[]>(propInitialNodes || initialNodes);
  const [edges, setEdges] = useState<Edge[]>(() => {
    const initialEdgeState = propInitialEdges || initialEdges;
    return initialEdgeState.map((edge) => ({
      ...edge,
      type: edge.type || "default",
    }));
  });
  const [selectedNode, setSelectedNode] = useState<Node<WorkflowNodeData> | null>(null);
  const [loopEdgeTrigger, setLoopEdgeTrigger] = useState(false);

  // Use the edge visibility hook to handle loop node edge rendering issues
  const { forceEdgeRefresh } = useEdgeVisibility(nodes, edges, setEdges);

  // Update nodes and edges when initialNodes or initialEdges change
  useEffect(() => {
    if (propInitialNodes) {
      const hasStartNode = propInitialNodes.some((node) => node.data.originalType === "StartNode");
      setNodes(hasStartNode ? propInitialNodes : [createStartNode(), ...propInitialNodes]);
    }
  }, [propInitialNodes]);

  useEffect(() => {
    if (propInitialEdges) {
      const edgesWithType = propInitialEdges.map((edge) => ({
        ...edge,
        type: edge.type || "default",
      }));
      setEdges(edgesWithType);
    }
  }, [propInitialEdges]);

  const memoizedNodes = useRef(nodes);
  const memoizedEdges = useRef(edges);
  const isEditingFieldRef = useRef(false);

  const setIsEditingField = useCallback((isEditing: boolean) => {
    isEditingFieldRef.current = isEditing;
  }, []);

  useEffect(() => {
    if (
      (nodes !== memoizedNodes.current || edges !== memoizedEdges.current) &&
      !isEditingFieldRef.current
    ) {
      memoizedNodes.current = nodes;
      memoizedEdges.current = edges;
      onFlowChange(nodes, edges);
    } else if (isEditingFieldRef.current) {
      memoizedNodes.current = nodes;
      memoizedEdges.current = edges;
    }
  }, [nodes, edges, onFlowChange]);

  // Initialize React Flow readiness state
  useEffect(() => {
    // Small delay to ensure React Flow is fully initialized
    const timer = setTimeout(() => {
      setIsReactFlowReady(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);

  // Removed automatic fitView to prevent coordinate system issues
  // useEffect(() => {
  //   if (nodes.length > 0) {
  //     setTimeout(() => fitView({ padding: 0.2 }), 100);
  //   }
  // }, [nodes, fitView]);

  const getConnectedNodes = useCallback(
    (sourceNodeId: string, edgesList: Edge[], visited = new Set<string>()): Set<string> => {
      if (visited.has(sourceNodeId)) return visited;
      visited.add(sourceNodeId);
      const outgoingEdges = edgesList.filter((edge) => edge.source === sourceNodeId);
      for (const edge of outgoingEdges) {
        getConnectedNodes(edge.target, edgesList, visited);
      }
      return visited;
    },
    [],
  );

  const getNodesConnectedToStartNode = useCallback((): Set<string> => {
    const startNode = nodes.find((node) => node.data.originalType === "StartNode");
    if (!startNode) return new Set<string>();
    return getConnectedNodesWithToolConnections(nodes, edges, startNode.id);
  }, [nodes, edges]);

  const updateNodeStyling = useCallback(() => {
    if (nodes.length === 0) return;
    const connectedNodes = getNodesConnectedToStartNode();
    setNodes((nds) => {
      const needsUpdate = nds.some((node) => {
        if (node.data.originalType === "StartNode") return false;
        const isConnected = connectedNodes.has(node.id);
        const currentOpacity = node.style?.opacity;
        return (isConnected && currentOpacity !== 1) || (!isConnected && currentOpacity !== 0.5);
      });
      if (!needsUpdate) return nds;
      return nds.map((node) => {
        if (node.data.originalType === "StartNode") return node;
        const isConnected = connectedNodes.has(node.id);
        return {
          ...node,
          style: { ...node.style, opacity: isConnected ? 1 : 0.5 },
        };
      });
    });
  }, [nodes, edges, getNodesConnectedToStartNode]);

  useEffect(() => {
    updateNodeStyling();
  }, [updateNodeStyling]);

  // Force re-render when loop node edge is added (removed fitView to prevent coordinate issues)
  useEffect(() => {
    if (loopEdgeTrigger) {
      // Force React Flow to recalculate and re-render edges without fitView
      const timer = setTimeout(() => {
        // Just trigger a re-render without changing viewport
        setLoopEdgeTrigger((prev) => prev);
      }, 50);
      
      return () => clearTimeout(timer);
    }
  }, [loopEdgeTrigger]);

  // Additional edge visibility fix - ensure edges are properly rendered after node changes
  useEffect(() => {
    const hasLoopNodes = nodes.some(node =>
      node.data.originalType === "LoopNode" || node.data.type === "loop"
    );
    
    if (hasLoopNodes && edges.length > 0) {
      // Small delay to ensure React Flow has processed all changes
      const timer = setTimeout(() => {
        // Force edge re-calculation by updating edge styles
        setEdges(currentEdges =>
          currentEdges.map(edge => ({
            ...edge,
            style: {
              ...edge.style,
              strokeWidth: edge.style?.strokeWidth || 2,
              stroke: edge.style?.stroke || "var(--primary)",
              zIndex: 5
            }
          }))
        );
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [nodes, edges.length]);

  const onNodesChange: OnNodesChange = useCallback((changes: NodeChange[]) => {
    setNodes((nds) => {
      const filteredChanges = changes.filter((change) => {
        if (change.type === "remove") {
          const node = nds.find((n) => n.id === change.id);
          if (node && node.data.originalType === "StartNode") return false;
        }
        return true;
      });
      return applyNodeChanges(filteredChanges, nds);
    });
  }, []);

  const onEdgesChange: OnEdgesChange = useCallback(
    (changes: EdgeChange[]) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [],
  );

  const onConnect: OnConnect = useCallback(
    (connection: Connection) => {
      const newEdge = { ...connection, type: "default" };
      setEdges((eds) => addEdge(newEdge, eds));

      const sourceNode = nodes.find((n) => n.id === connection.source);
      const targetNode = nodes.find((n) => n.id === connection.target);

      // Check if either node is a loop node or any node that might have rendering issues
      const needsRefresh =
        sourceNode?.data.originalType === "LoopNode" ||
        targetNode?.data.originalType === "LoopNode" ||
        sourceNode?.data.type === "loop" ||
        targetNode?.data.type === "loop";

      if (needsRefresh) {
        // Trigger immediate re-render for loop nodes
        setLoopEdgeTrigger((prev) => !prev);
        
        // Use the edge visibility hook for more robust handling
        setTimeout(() => {
          forceEdgeRefresh();
        }, 50);
      }
    },
    [setEdges, nodes, forceEdgeRefresh],
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();
      if (!reactFlowWrapper.current) return;

      const dataString = event.dataTransfer.getData("application/reactflow");
      if (!dataString) return;

      // Define a more specific type for the parsed data, including optional loop_config
      const { nodeType: originalNodeType, definition } = JSON.parse(dataString) as {
        nodeType: string;
        definition: ComponentDefinition & { loop_config?: any };
      };

      const bounds = reactFlowWrapper.current.getBoundingClientRect();
      
      // Calculate the mouse position relative to the canvas
      const clientX = event.clientX - bounds.left;
      const clientY = event.clientY - bounds.top;
      
      // Use screenToFlowPosition with error handling for coordinate system issues
      let rawPosition;
      try {
        // If React Flow is not ready yet, wait a bit and use fallback
        if (!isReactFlowReady) {
          console.warn("React Flow not ready, using fallback positioning");
          throw new Error("React Flow not ready");
        }
        
        rawPosition = screenToFlowPosition({
          x: clientX,
          y: clientY,
        });
        
        // Validate the position - if it's invalid (NaN, negative, or extremely large), use fallback
        if (
          !rawPosition ||
          isNaN(rawPosition.x) ||
          isNaN(rawPosition.y) ||
          rawPosition.x < -10000 ||
          rawPosition.y < -10000 ||
          rawPosition.x > 10000 ||
          rawPosition.y > 10000
        ) {
          throw new Error("Invalid position returned from screenToFlowPosition");
        }
      } catch (error) {
        console.warn("screenToFlowPosition failed, using fallback positioning:", error);
        // Fallback: Use a simple coordinate calculation based on current viewport
        // This ensures the node appears near where the user dropped it
        rawPosition = {
          x: clientX,
          y: clientY,
        };
      }
      
      // Node dimensions - width is approximately 208px (w-52)
      const nodeWidth = 208;
      const nodeHeight = 90;
      
      // Adjust position to center the node on the drop point
      // Since we changed nodeOrigin to [0, 0], we need to offset by half the node size
      const adjustedPosition = {
        x: rawPosition.x - nodeWidth / 2,
        y: rawPosition.y - nodeHeight / 2,
      };
      
      // Ensure the dropped node is always visible by setting minimum constraints
      const minX = 10; // Minimum distance from left edge
      const minY = 10; // Minimum distance from top edge
      
      const position = {
        x: Math.max(adjustedPosition.x, minX),
        y: Math.max(adjustedPosition.y, minY),
      };

      const isMCPComponent = originalNodeType.includes("MCP_");
      const isAgentComponent = originalNodeType === "AgenticAI" || originalNodeType.startsWith("agent-");
      const isWorkflowComponent = originalNodeType.startsWith("workflow-");
      const isLoopNode = originalNodeType === "LoopNode";
      let nodeType = "component";
      if (isMCPComponent) nodeType = "mcp";
      else if (isAgentComponent) {
        // Check if this is an employee agent (from /agents endpoint)
        const isEmployeeAgent = originalNodeType.startsWith("agent-");
        nodeType = isEmployeeAgent ? "employee" : "agent";
      }
      else if (isWorkflowComponent) nodeType = "workflows";
      else if (isLoopNode) nodeType = "loop";

      // ======================= START: ENHANCED FIX IMPLEMENTATION =======================
      // Initialize config. For any node providing a default config in its
      // definition (like LoopNode), copy it into the node's instance data.
      const initialConfig: { [key: string]: any } = {};

      if (definition.loop_config) {
        // Deep copy to prevent reference issues if the definition object is reused.
        initialConfig.loop_config = JSON.parse(JSON.stringify(definition.loop_config));
        console.log("Initialized LoopNode with default config:", initialConfig.loop_config);

        // Ensure LoopNode has default input handle if missing
        if (!definition.inputs || definition.inputs.length === 0) {
          definition.inputs = [
            {
              name: "loop_input",
              display_name: "Loop Input",
              input_types: ["Any"],
              is_handle: true,
              input_type: "handle",
              required: false,
              is_list: false,
              info: "Input for loop iteration",
              real_time_refresh: false,
              advanced: false,
              value: null,
              options: null,
              visibility_rules: null,
              visibility_logic: "OR",
              requirement_rules: null,
              requirement_logic: "OR"
            }
          ];
        }
      }

      // Create a temporary node to use with populateDefaultValues
      const tempNode: Node<WorkflowNodeData> = {
        id: `${originalNodeType}-${+new Date()}`,
        type: "WorkflowNode",
        position,
        data: {
          label: definition.display_name,
          type: nodeType,
          originalType: originalNodeType,
          definition: definition,
          config: initialConfig,
        },
      };

      // Populate default values from input definitions immediately
      const configWithDefaults = populateDefaultValues(tempNode);

      // Create the final node with populated default values
      const newNode: Node<WorkflowNodeData> = {
        ...tempNode,
        data: {
          ...tempNode.data,
          config: configWithDefaults,
        },
      };

      if (process.env.NODE_ENV === 'development') {
        console.log(`Created node ${originalNodeType} with populated defaults:`, configWithDefaults);
      }
      // ======================== END: ENHANCED FIX IMPLEMENTATION ========================

      setNodes((nds) => nds.concat(newNode));
    },
    [screenToFlowPosition, setNodes, isReactFlowReady],
  );

  const onNodeClick = useCallback((_: React.MouseEvent, node: Node<WorkflowNodeData>) => {
    setSelectedNode(node);
  }, []);

  const handleCloseInspector = () => setSelectedNode(null);

  const handleDeleteNode = useCallback(
    (nodeId: string) => {
      const node = nodes.find((n) => n.id === nodeId);
      if (node && node.data.originalType === "StartNode") return;

      setNodes((nds) => nds.filter((n) => n.id !== nodeId));
      setEdges((eds) => eds.filter((e) => e.source !== nodeId && e.target !== nodeId));
      setSelectedNode(null);
    },
    [nodes, setNodes, setEdges],
  );

  const handleNodeDataChange = useCallback(
    (nodeId: string, newData: WorkflowNodeData) => {
      setNodes((nds) =>
        nds.map((node) => (node.id === nodeId ? { ...node, data: newData } : node)),
      );
      setSelectedNode((prev) => (prev && prev.id === nodeId ? { ...prev, data: newData } : prev));
    },
    [setNodes],
  );

  useEffect(() => {
    const handleKeyDown = (event: globalThis.KeyboardEvent) => {
      if (
        (event.key === "Delete" || event.key === "Backspace") &&
        selectedNode &&
        !(event.target instanceof HTMLInputElement) &&
        !(event.target instanceof HTMLTextAreaElement)
      ) {
        handleDeleteNode(selectedNode.id);
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [selectedNode, handleDeleteNode]);

  const [copiedNode, setCopiedNode] = useState<Node<WorkflowNodeData> | null>(null);

  const handleCopyNode = useCallback(() => {
    if (selectedNode) setCopiedNode(selectedNode);
  }, [selectedNode]);

  const handlePasteNode = useCallback(() => {
    if (!copiedNode) return;

    const originalType = copiedNode.data.originalType || copiedNode.data.type;
    const newId = `${originalType}-${+new Date()}`;
    const newPosition = { x: copiedNode.position.x + 50, y: copiedNode.position.y + 50 };

    // ======================= START: ENHANCED ROBUSTNESS FIX FOR PASTE =======================
    // Re-apply the default config logic to ensure pasted nodes are also correct,
    // even if they were copied from an older, buggy state.
    const definition = copiedNode.data.definition as ComponentDefinition & { loop_config?: any };
    const baseConfig = { ...(copiedNode.data.config || {}) };

    // If the pasted node is a LoopNode but is missing its config, add the default.
    if (originalType === "LoopNode" && !baseConfig.loop_config && definition?.loop_config) {
      baseConfig.loop_config = JSON.parse(JSON.stringify(definition.loop_config));
      console.log("Fixed missing loop_config on pasted node:", newId);
    }

    // Create a temporary node to use with populateDefaultValues
    const tempPastedNode: Node<WorkflowNodeData> = {
      ...copiedNode,
      id: newId,
      position: newPosition,
      selected: false,
      data: {
        ...copiedNode.data,
        config: baseConfig,
      },
    };

    // Populate default values from input definitions for pasted node
    const configWithDefaults = populateDefaultValues(tempPastedNode);

    const newNode: Node<WorkflowNodeData> = {
      ...tempPastedNode,
      data: {
        ...tempPastedNode.data,
        config: configWithDefaults,
      },
    };

    if (process.env.NODE_ENV === 'development') {
      console.log(`Pasted node ${originalType} with populated defaults:`, configWithDefaults);
    }
    // ======================== END: ENHANCED ROBUSTNESS FIX FOR PASTE ========================

    setNodes((nds) => nds.concat(newNode));
  }, [copiedNode, setNodes]);

  return (
    <div className="relative h-full w-full flex-grow overflow-hidden" ref={reactFlowWrapper}>
      <ReactFlow
        key={`reactflow-${loopEdgeTrigger}`}
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onNodeClick={onNodeClick}
        onPaneClick={handleCloseInspector}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        nodeOrigin={nodeOrigin}
        className="bg-background"
        snapToGrid={true}
        snapGrid={[20, 20]}
        nodesDraggable={true}
        nodesConnectable={true}
        elementsSelectable={true}
        selectNodesOnDrag={false}
        panOnDrag={true}
        zoomOnScroll={true}
        zoomOnPinch={true}
        zoomOnDoubleClick={false}
        preventScrolling={false}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        minZoom={0.1}
        maxZoom={4}
        defaultEdgeOptions={{
          animated: true,
          style: { strokeWidth: 2, stroke: "var(--primary)", zIndex: 5 },
        }}
      >
        <Controls showInteractive={false} className="bg-card rounded-lg border p-1 shadow-md" />
        <MiniMap
          nodeStrokeWidth={3}
          zoomable
          pannable
          nodeColor={nodeColor}
          className="bg-card/80 rounded-lg border shadow-md backdrop-blur-sm"
        />
        <Background
          variant={BackgroundVariant.Dots}
          gap={20}
          size={1}
          color="var(--border)"
          className="bg-background/50"
        />
      </ReactFlow>

      <InspectorPanel
        selectedNode={selectedNode}
        onNodeDataChange={handleNodeDataChange}
        onClose={handleCloseInspector}
        onDeleteNode={handleDeleteNode}
        edges={edges}
        nodes={nodes}
        setIsEditingField={setIsEditingField}
        applicationData={applicationData}
        onRemoveToolSlot={() => {}}
        onAddToolSlot={() => {}}
      />
    </div>
  );
}

// Wrapper component updated to pass application data
const WorkflowCanvasWrapper = React.memo(function WorkflowCanvasWrapper({
  onFlowChange,
  initialWorkflow,
  applicationData,
}: {
  onFlowChange: (nodes: Node<WorkflowNodeData>[], edges: Edge[]) => void;
  initialWorkflow?: { nodes: Node<WorkflowNodeData>[]; edges: Edge[] };
  applicationData?: ApplicationData;
}) {
  return (
    <ReactFlowProvider>
      <FlowCanvas
        onFlowChange={onFlowChange}
        initialNodes={initialWorkflow?.nodes}
        initialEdges={initialWorkflow?.edges}
        applicationData={applicationData}
      />
    </ReactFlowProvider>
  );
});

export default WorkflowCanvasWrapper;
