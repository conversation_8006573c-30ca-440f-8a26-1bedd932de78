"use client";

import "reactflow/dist/style.css";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useTheme } from "next-themes";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Sidebar } from "@/components/layout/Sidebar";
import { TopBar } from "@/components/layout/TopBar";
import WorkflowCanvasWrapper from "@/components/canvas/WorkflowCanvas";
import { WorkflowNodeData } from "@/types";
import { ComponentsApiResponse } from "@/types";
import { MCP_CATEGORY_LABELS, MCPCategory } from "@/types/mcp";
import {
  fetchComponents,
  fetchRawComponents,
  fetchMCPComponents,
  fetchMCPCategories,
  fetchMCPComponentsByCategory,
  fetchProviders,
  fetchModels,
  fetchCredentials,
  ExecutionResult,
  saveWorkflowToServer,
} from "@/lib/api";
import { enhanceComponentsWithDynamicData } from "@/lib/componentEnhancer";
import { saveWorkflowToFile, loadWorkflowFromJson } from "@/lib/workflowUtils";
import { fetchWorkflowById, fetchWorkflowFromBuilderUrl, fetchWorkflowsByUser, WorkflowListItem } from "@/lib/workflowApi";
import { LoadWorkflowModal } from "@/components/modals/LoadWorkflowModal";
import { UnsavedChangesDialog } from "@/components/modals/UnsavedChangesDialog";
import { Node, Edge } from "reactflow";
import { Loader2 } from "lucide-react";
import { useValidationStore } from "@/store/validationStore";
import { useExecutionStore } from "@/store/executionStore";
import { WorkflowSummary } from "@/app/(features)/workflows/api";

// Define a proper type for workflow data
interface WorkflowData {
  nodes: Node<WorkflowNodeData>[];
  edges: Edge[];
  workflow_name?: string;
  [key: string]: unknown; // Allow for additional properties
}

interface WorkflowEditorClientProps {
  id: string;
}

export default function WorkflowEditorClient({ id }: WorkflowEditorClientProps) {
  const router = useRouter();

  // State for loading components
  const [componentsData, setComponentsData] = useState<ComponentsApiResponse>({});
  // Memoize components to prevent unnecessary re-renders of the Sidebar
  const components = useMemo(() => componentsData, [componentsData]);
  // Rename for clarity
  const [isLoadingComponents, setIsLoadingComponents] = useState(true);
  const [componentError, setComponentError] = useState<string | null>(null);

  // State for application data (providers, models, credentials)
  const [providers, setProviders] = useState<any[]>([]);
  const [models, setModels] = useState<any[]>([]);
  const [credentials, setCredentials] = useState<any[]>([]);
  const [isLoadingAppData, setIsLoadingAppData] = useState(true);
  const [appDataError, setAppDataError] = useState<string | null>(null);

  // State for the workflow itself
  const [currentNodes, setCurrentNodes] = useState<Node<WorkflowNodeData>[]>([]);
  const [currentEdges, setCurrentEdges] = useState<Edge[]>([]);

  // State for workflow title and theme
  const [workflowTitle, setWorkflowTitle] = useState("Untitled Workflow");
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  
  // Determine if dark mode is active
  const isDarkMode = theme === "dark";

  // State for execution status
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<ExecutionResult | null>(null);

  // State for workflow loading
  const [isLoadModalOpen, setIsLoadModalOpen] = useState(false);
  const [loadedWorkflow, setLoadedWorkflow] = useState<{
    nodes: Node<WorkflowNodeData>[];
    edges: Edge[];
  } | null>(null);

  // State for sidebar collapse
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // State for unsaved changes dialog
  const [isUnsavedChangesDialogOpen, setIsUnsavedChangesDialogOpen] = useState(false);
  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);

  // State for workflow loading from URL
  const [isLoadingWorkflow, setIsLoadingWorkflow] = useState(false);
  const [workflowLoadError, setWorkflowLoadError] = useState<string | null>(null);

  // State for tracking unsaved changes
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // State for lazy loading MCP categories
  const [mcpCategories, setMcpCategories] = useState<string[]>([]);
  const [loadedMcpCategories, setLoadedMcpCategories] = useState<Set<string>>(new Set());

  // State for workflow components
  const [workflowComponents, setWorkflowComponents] = useState<WorkflowListItem[]>([]);
  const [isLoadingWorkflows, setIsLoadingWorkflows] = useState(false);

  // Handler for loading a workflow
  const handleLoadWorkflow = useCallback(
    (workflowData: WorkflowData, showSuccessMessage: boolean = true) => {
      try {
        console.log("Loading workflow data:", workflowData);

        // Validate and convert the workflow data
        const result = loadWorkflowFromJson(workflowData);
        console.log("Validation result:", result);

        if (!result.isValid || !result.data) {
          // Use a more modern notification approach instead of alert
          console.error(`Failed to load workflow: ${result.error || "Invalid workflow data"}`);
          return;
        }

        // Check if the workflow data has a StartNode and ensure its configuration is preserved
        const nodes = result.data.nodes;
        const startNode = nodes.find(
          (node: Node<WorkflowNodeData>) => node.data.originalType === "StartNode",
        );

        if (startNode) {
          console.log("StartNode found in loaded workflow:", startNode);

          // Ensure the StartNode has a config object with collected_parameters
          if (!startNode.data.config) {
            startNode.data.config = {};
          }

          if (!startNode.data.config.collected_parameters) {
            startNode.data.config.collected_parameters = {};
          }

          // Ensure all parameters in collected_parameters have the required property set
          // This is critical for pre-built workflows where the required property might not be set
          if (startNode.data.config.collected_parameters) {
            Object.keys(startNode.data.config.collected_parameters).forEach((paramId) => {
              const param = startNode.data.config.collected_parameters[paramId];
              // If required is undefined, set it to true (consider required unless explicitly false)
              if (param.required === undefined) {
                console.log(`Setting required=true for parameter ${paramId} in StartNode`);
                param.required = true;
              }
            });
          }

          console.log("StartNode config after ensuring structure:", startNode.data.config);

          // Store the StartNode's collected parameters in the window object
          // This will make them available to the ExecutionDialog component
          window.startNodeCollectedParameters = startNode.data.config?.collected_parameters || {};
          console.log(
            "Stored StartNode collected parameters in window object:",
            window.startNodeCollectedParameters,
          );
        } else {
          console.log("No StartNode found in loaded workflow, this may cause issues");
          // Clear any previously stored parameters
          window.startNodeCollectedParameters = {};
        }

        // Update the workflow state
        console.log("Setting workflow state with:", result.data);
        setLoadedWorkflow(result.data);
        setCurrentNodes(result.data.nodes);
        setCurrentEdges(result.data.edges);

        // Update the workflow title if available
        if (workflowData.workflow_name) {
          console.log(
            "Setting workflow title from workflowData.workflow_name:",
            workflowData.workflow_name,
          );
          setWorkflowTitle(workflowData.workflow_name);
        } else {
          console.log("No workflow_name found in workflowData, keeping current title");
        }

        // Reset unsaved changes flag after successful load
        setHasUnsavedChanges(false);

        // Only show success message if explicitly requested
        // This prevents showing alerts when loading from URL parameters
        if (showSuccessMessage) {
          // Use a temporary notification in the UI instead of an alert
          setExecutionResult({
            success: true,
            message: "Workflow loaded successfully!",
          });

          // Auto-hide the notification after 3 seconds
          setTimeout(() => {
            setExecutionResult(null);
          }, 3000);
        }
      } catch (error) {
        console.error("Error loading workflow:", error);
        // Use a more modern notification approach instead of alert
        setExecutionResult({
          success: false,
          error: `Error loading workflow: ${error instanceof Error ? error.message : "Unknown error"}`,
        });

        // Auto-hide the error notification after 5 seconds
        setTimeout(() => {
          setExecutionResult(null);
        }, 5000);
      }
    },
    [], // Remove workflowTitle from dependencies - it's only used in a console.log
  );

  // Effect to load workflow from ID parameter
  useEffect(() => {
    const loadWorkflowFromId = async () => {
      if (!id) {
        router.push("/workflows");
        return;
      }

      setIsLoadingWorkflow(true);
      setWorkflowLoadError(null);

      try {
        // Fetch workflow details to get the builder_url
        const workflowDetails: WorkflowSummary = await fetchWorkflowById(id);
        console.log("Workflow details:", workflowDetails);

        if (
          !workflowDetails ||
          !workflowDetails.workflow ||
          !workflowDetails.workflow.builder_url
        ) {
          console.error(
            "Workflow data URL not found inside workflow details.",
            workflowDetails?.workflow?.builder_url,
          );
          throw new Error("Workflow data URL not found");
        }

        // Fetch the actual workflow data from builder_url
        const workflowData: WorkflowData = await fetchWorkflowFromBuilderUrl(
          workflowDetails.workflow.builder_url,
        );

        // Ensure the workflow_name is set in the workflowData
        if (!workflowData.workflow_name && workflowDetails.workflow.name) {
          console.log("Setting workflow_name from workflowDetails:", workflowDetails.workflow.name);
          workflowData.workflow_name = workflowDetails.workflow.name;
        }

        console.log("Final workflowData before loading:", workflowData);

        // Load the workflow into the canvas without showing success message
        handleLoadWorkflow(workflowData, false);
      } catch (error) {
        console.error("Error loading workflow:", error);
        setWorkflowLoadError(
          error instanceof Error ? error.message : "Failed to load workflow. Please try again.",
        );
      } finally {
        setIsLoadingWorkflow(false);
      }
    };

    loadWorkflowFromId();
  }, [id, router, handleLoadWorkflow]);

  // Effect to load components and application data on mount
  useEffect(() => {
    const loadComponents = async (preloadedAppData?: {
      providers: any[];
      models: any[];
      credentials: any[];
    }) => {
      try {
        setIsLoadingComponents(true);
        setComponentError(null);

        // Fetch raw components (without enhancement)
        const rawComponents = await fetchRawComponents();

        // Enhance components with preloaded data if available
        const fetchedComponents = preloadedAppData
          ? await enhanceComponentsWithDynamicData(rawComponents, preloadedAppData)
          : await enhanceComponentsWithDynamicData(rawComponents);
        console.log(
          "DEBUG: Fetched regular components categories:",
          Object.keys(fetchedComponents),
        );

        // Log AI components if they exist
        if (fetchedComponents.AI) {
          console.log("DEBUG: AI components found:", Object.keys(fetchedComponents.AI));
        } else {
          console.log("DEBUG: No AI category found in components");
        }

        // Fetch MCP categories only (lazy loading approach)
        const mcpCategoriesResponse = await fetchMCPCategories();
        console.log("DEBUG: Fetched MCP categories:", mcpCategoriesResponse);
        
        // Set the MCP categories for lazy loading
        setMcpCategories(mcpCategoriesResponse);

        // Create empty placeholders for MCP categories in components data
        const mcpCategoryPlaceholders: { [key: string]: any } = {};
        mcpCategoriesResponse.forEach((category: string) => {
          mcpCategoryPlaceholders[category] = {}; // Empty object as placeholder
        });

        // Merge the components with MCP category placeholders
        const mergedComponents = {
          ...fetchedComponents,
          ...mcpCategoryPlaceholders,
        };

        console.log("DEBUG: Merged components with MCP placeholders:", Object.keys(mergedComponents));
        setComponentsData(mergedComponents);
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : "Unknown error";
        setComponentError(`Failed to load components: ${errorMsg}. Ensure the backend is running.`);
        console.error(err);
      } finally {
        setIsLoadingComponents(false);
      }
    };

    const loadApplicationData = async () => {
      try {
        setIsLoadingAppData(true);
        setAppDataError(null);

        console.log("Loading application data (providers, models, credentials)...");

        // Load providers, models, and credentials in parallel
        const [providersResponse, modelsResponse, credentialsResponse] = await Promise.all([
          fetchProviders(),
          fetchModels(),
          fetchCredentials(),
        ]);

        let loadedProviders: any[] = [];
        let loadedModels: any[] = [];
        let loadedCredentials: any[] = [];

        // Handle providers
        if (providersResponse.success) {
          loadedProviders = providersResponse.providers;
          setProviders(loadedProviders);
          console.log(`✅ Loaded ${loadedProviders.length} providers`);
        } else {
          console.warn("Failed to fetch providers:", providersResponse.message);
          setProviders([]);
        }

        // Handle models
        if (modelsResponse.success) {
          loadedModels = modelsResponse.models;
          setModels(loadedModels);
          console.log(`✅ Loaded ${loadedModels.length} models`);
        } else {
          console.warn("Failed to fetch models:", modelsResponse.message);
          setModels([]);
        }

        // Handle credentials
        if (credentialsResponse.credentials) {
          loadedCredentials = credentialsResponse.credentials;
          setCredentials(loadedCredentials);
          console.log(`✅ Loaded ${loadedCredentials.length} credentials`);
        } else {
          console.warn("Failed to fetch credentials: No credentials found in response");
          setCredentials([]);
        }

        console.log("✅ Application data loading completed");
        console.log("📊 Application data summary:", {
          providers: loadedProviders.length,
          models: loadedModels.length,
          credentials: loadedCredentials.length,
        });

        // Return the loaded data
        return {
          providers: loadedProviders,
          models: loadedModels,
          credentials: loadedCredentials,
        };

      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : "Unknown error";
        setAppDataError(`Failed to load application data: ${errorMsg}`);
        console.error("❌ Failed to load application data:", err);
        throw err;
      } finally {
        setIsLoadingAppData(false);
      }
    };

    console.log("Loading components and application data...");

    // Load application data first, then components with preloaded data
    const loadAllData = async () => {
      try {
        // Load application data first
        const loadedAppData = await loadApplicationData();

        // Pass the loaded application data to component loading
        await loadComponents(loadedAppData);
        
        // Load workflows for components after components are loaded (like agents)
        await loadWorkflowsForComponents();
        
        console.log("✅ All data loading completed");
      } catch (error) {
        console.error("❌ Error during data loading:", error);
      }
    };

    loadAllData();
  }, []); // Empty dependency array ensures this runs once on mount

  // Callback when nodes/edges change in the canvas
  const handleFlowChange = useCallback((nodes: Node<WorkflowNodeData>[], edges: Edge[]) => {
    setCurrentNodes(nodes);
    setCurrentEdges(edges);
    // Mark as having unsaved changes when the flow is updated
    setHasUnsavedChanges(true);
  }, []); // Empty dependency array means this callback is stable

  // Callback for the Save to Server button
  const handleSaveWorkflowToServer = useCallback(async () => {
    try {
      // Get the current workflow title from state
      const filename = `${workflowTitle.replace(/\s+/g, "_")}`;

      // Call the API function to save to server
      const result = await saveWorkflowToServer({
        nodes: currentNodes,
        edges: currentEdges,
        filename: filename, // Use workflow title as filename
        workflow_name: workflowTitle, // Include the workflow name
        workflow_id: id, // Use the ID from the URL parameter
      });

      if (result.success) {
        // Reset unsaved changes flag
        setHasUnsavedChanges(false);

        // Use the message field if available, otherwise use the default success message
        alert(result.message || `Workflow saved successfully on server at: ${result.filepath}`);
      } else {
        alert(`Failed to save workflow on server: ${result.error || "Unknown error"}`);
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : String(err);
      console.error("Error in handleSaveWorkflowToServer:", err);
      alert(`Error saving workflow to server: ${errorMsg}`);
    }
  }, [currentNodes, currentEdges, workflowTitle, id]); // Depends on the current flow state and ID

  // Callback for the Save button - validates and saves the workflow
  const handleSaveWorkflow = useCallback(async () => {
    try {
      // First validate the workflow
      const { validateBeforeSave } = useValidationStore.getState();
      const validationResult = await validateBeforeSave(currentNodes, currentEdges);

      if (!validationResult.isValid) {
        // Show validation errors
        const errorMessages = validationResult.errors.map((err) => err.message).join("\n");
        toast.error(`Validation failed. Please fix the following issues:\n${errorMessages}`);
        return;
      }

      // Proceed with saving to server
      const filename = `${workflowTitle.replace(/\s+/g, "_")}`;

      const result = await saveWorkflowToServer({
        nodes: currentNodes,
        edges: currentEdges,
        filename: filename,
        workflow_name: workflowTitle,
        workflow_id: id, // Use the ID from the URL parameter
      });

      if (result.success) {
        // Reset unsaved changes flag
        setHasUnsavedChanges(false);

        // Show success message
        toast.success("Workflow saved successfully!");
      } else {
        // Show error message
        toast.error(`Failed to save workflow: ${result.error || "Unknown error"}`);
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : String(err);
      console.error("Error in handleSaveWorkflow:", err);
      toast.error(`Error saving workflow: ${errorMsg}`);
    }
  }, [currentNodes, currentEdges, workflowTitle, id]); // Depends on the current flow state and ID

  // --- Callback for the Run button ---
  const handleRunWorkflow = useCallback(async () => {
    if (isExecuting) return; // Prevent multiple simultaneous runs

    setIsExecuting(true);
    setExecutionResult(null); // Clear previous results

    try {
      // First validate the workflow
      const { validateBeforeExecution } = useValidationStore.getState();
      const validationResult = await validateBeforeExecution(currentNodes, currentEdges);

      if (!validationResult.isValid) {
        // Show validation errors
        const errorMessages = validationResult.errors.map((err) => err.message).join("\n");
        alert(`Validation failed. Please fix the following issues:\n${errorMessages}`);
        setIsExecuting(false);
        return;
      }

      // Save the workflow first
      const filename = `${workflowTitle.replace(/\s+/g, "_")}`;
      const saveResult = await saveWorkflowToServer({
        nodes: currentNodes,
        edges: currentEdges,
        filename: filename,
        workflow_name: workflowTitle,
        workflow_id: id, // Use the ID from the URL parameter
      });

      if (!saveResult.success) {
        alert(`Failed to save workflow before execution: ${saveResult.error || "Unknown error"}`);
        setIsExecuting(false);
        return;
      }

      // Reset unsaved changes flag after successful save
      setHasUnsavedChanges(false);

      // Get the execution store
      const executionStore = useExecutionStore.getState();

      // Set the missing fields from the validation result
      if (validationResult.missingFields) {
        executionStore.setMissingFields(validationResult.missingFields);
      }

      // Open the execution dialog
      executionStore.setDialogOpen(true);
      executionStore.setActiveTab("parameters");

      // Add a log entry
      executionStore.addLog("Workflow validated and saved successfully. Ready for execution.");

      // Set executing state to false since we're just opening the dialog
      setIsExecuting(false);

      // The actual execution will be handled by the ExecutionDialog component
      // when the user clicks the "Run" button in the dialog
    } catch (err) {
      // Catch unexpected errors
      const errorMsg = err instanceof Error ? err.message : String(err);
      console.error("Error in handleRunWorkflow:", err);
      setExecutionResult({ success: false, error: `Frontend error: ${errorMsg}` });
      alert(`Error preparing workflow for execution: ${errorMsg}`);
      setIsExecuting(false);
    }
  }, [currentNodes, currentEdges, isExecuting, id, workflowTitle, setHasUnsavedChanges]); // Dependencies

  // Theme toggle handler
  const handleToggleTheme = useCallback(() => {
    setTheme(theme === "dark" ? "light" : "dark");
  }, [theme, setTheme]);

  // Function to handle actions that might discard unsaved changes
  const handleActionWithUnsavedChanges = useCallback(
    (action: () => void) => {
      if (hasUnsavedChanges) {
        // If there are unsaved changes, show the confirmation dialog
        setPendingAction(() => action);
        setIsUnsavedChangesDialogOpen(true);
      } else {
        // If no unsaved changes, proceed with the action
        action();
      }
    },
    [hasUnsavedChanges],
  );

  // Handler for opening the load workflow modal
  const handleOpenLoadModal = useCallback(() => {
    handleActionWithUnsavedChanges(() => {
      setIsLoadModalOpen(true);
    });
  }, [handleActionWithUnsavedChanges]);

  // Effect to handle mounting and theme initialization
  useEffect(() => {
    setMounted(true);
  }, []);

  // Effect to add beforeunload event listener for unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        // Standard way to show a confirmation dialog when leaving the page
        const message = "You have unsaved changes. Are you sure you want to leave?";
        e.preventDefault();
        // Modern browsers require both preventDefault and setting returnValue
        // Even though returnValue is deprecated, it's still required for this to work
        e.returnValue = message;
        return message;
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // Memoized callback for toggling sidebar
  const handleToggleSidebar = useCallback(() => {
    setIsSidebarCollapsed((prev) => !prev);
  }, []);

  // Effect to add keyboard shortcut for toggling sidebar
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Toggle sidebar with Alt+S
      if (e.altKey && e.key === "s") {
        handleToggleSidebar();
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleToggleSidebar]);

  // Handle title change and save workflow
  const handleTitleChange = useCallback(
    (newTitle: string) => {
      // Update the title state
      setWorkflowTitle(newTitle);

      // Save the workflow with the new title
      const filename = `${newTitle.replace(/\s+/g, "_")}`;
      saveWorkflowToServer({
        nodes: currentNodes,
        edges: currentEdges,
        filename: filename,
        workflow_name: newTitle,
        workflow_id: id,
      })
        .then((result) => {
          if (result.success) {
            // Reset unsaved changes flag
            setHasUnsavedChanges(false);
            toast.success("Workflow title updated successfully");
          } else {
            toast.error(`Failed to update workflow title: ${result.error || "Unknown error"}`);
          }
        })
        .catch((err) => {
          const errorMsg = err instanceof Error ? err.message : String(err);
          toast.error(`Error updating workflow title: ${errorMsg}`);
        });
    },
    [currentNodes, currentEdges, id],
  );

  // Function to load MCP components for a specific category (lazy loading)
  const loadMCPComponentsForCategory = useCallback(async (category: string) => {
    // Check if already loaded
    if (loadedMcpCategories.has(category)) {
      console.log(`DEBUG: Category "${category}" already loaded, skipping`);
      return;
    }

    try {
      console.log(`DEBUG: Loading MCP components for category: ${category}`);
      const categoryComponents = await fetchMCPComponentsByCategory(category);
      console.log(`DEBUG: Loaded MCP components response for category "${category}":`, categoryComponents);

      // Get the display label for this enum category
      const categoryDisplayLabel = MCP_CATEGORY_LABELS[category as MCPCategory] || category;
      console.log(`DEBUG: Category "${category}" maps to display label "${categoryDisplayLabel}"`);

      // Update the components data with the loaded category components
      setComponentsData(prevData => {
        const updatedData = { ...prevData };
        
        // The fetchMCPComponentsByCategory returns a ComponentsApiResponse object
        // We need to merge all categories from the response into our components data
        Object.entries(categoryComponents).forEach(([responseCategory, components]) => {
          if (components && typeof components === 'object' && Object.keys(components).length > 0) {
            // Store the components under the display label key (what the sidebar expects)
            updatedData[categoryDisplayLabel] = components;
            console.log(`DEBUG: Updated category "${categoryDisplayLabel}" with ${Object.keys(components).length} components`);
          }
        });

        // If no components were found in any category, still create the display label category
        const totalComponents = Object.values(categoryComponents).reduce((total, categoryData) => {
          return total + (categoryData && typeof categoryData === 'object' ? Object.keys(categoryData).length : 0);
        }, 0);
        
        if (totalComponents === 0) {
          console.log(`DEBUG: No components found for category "${category}" in API response`);
          // Create the display label category with empty object to show "No MCP Added" message
          updatedData[categoryDisplayLabel] = {};
        }

        console.log(`DEBUG: Final updated categories:`, Object.keys(updatedData));
        return updatedData;
      });

      // Mark this category as loaded (using the enum key)
      setLoadedMcpCategories(prev => new Set([...prev, category]));
      
      console.log(`DEBUG: Successfully loaded and cached category "${category}" as "${categoryDisplayLabel}"`);
    } catch (error) {
      console.error(`Error loading MCP components for category "${category}":`, error);
      
      // Get the display label for error handling too
      const categoryDisplayLabel = MCP_CATEGORY_LABELS[category as MCPCategory] || category;
      
      // On error, still mark as loaded but with empty data to prevent infinite loading
      setComponentsData(prevData => ({
        ...prevData,
        [categoryDisplayLabel]: {} // Empty object to show "No MCP Added" message
      }));
      setLoadedMcpCategories(prev => new Set([...prev, category]));
      
      // Show error notification
      toast.error(`Failed to load MCP components for ${categoryDisplayLabel}`);
    }
  }, [loadedMcpCategories]);

  // Function to load workflows for component use
  const loadWorkflowsForComponents = useCallback(async () => {
    if (isLoadingWorkflows) return; // Prevent duplicate loading
    
    try {
      setIsLoadingWorkflows(true);
      console.log("Loading workflows for component use...");
      
      // Use the same API as the workflows page, but exclude current workflow
      const workflowsResponse = await fetchWorkflowsByUser(1, 50); // Load first 50 workflows
      
      // Filter out current workflow to prevent self-inclusion
      const filteredWorkflows = workflowsResponse.data.filter(
        (workflow) => workflow.id !== id
      );
      
      setWorkflowComponents(filteredWorkflows);
      console.log(`Loaded ${filteredWorkflows.length} workflows for components`, filteredWorkflows);
    } catch (error) {
      console.error("Error loading workflows for components:", error);
      setWorkflowComponents([]);
      toast.error("Failed to load workflows");
    } finally {
      setIsLoadingWorkflows(false);
    }
  }, [id, isLoadingWorkflows]);

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return null;
  }

  return (
    <main className="relative flex h-screen w-screen flex-col overflow-hidden">
      <TopBar
        onSave={handleSaveWorkflow}
        onRun={handleRunWorkflow}
        workflowTitle={workflowTitle}
        onTitleChange={handleTitleChange}
        onValidate={() => console.log("Validate workflow")}
        isDarkMode={isDarkMode}
        onToggleTheme={handleToggleTheme}
        onLoad={handleOpenLoadModal}
        className="flex-shrink-0"
        nodes={currentNodes}
        edges={currentEdges}
        workflowId={id}
      />

      <div className="flex flex-grow overflow-hidden">
        <Sidebar
          components={components}
          collapsed={isSidebarCollapsed}
          onToggleCollapse={handleToggleSidebar}
          mcpCategories={mcpCategories}
          loadedMcpCategories={loadedMcpCategories}
          onLoadMCPCategory={loadMCPComponentsForCategory}
          workflowComponents={workflowComponents}
          isLoadingWorkflows={isLoadingWorkflows}
        />
        <div className="bg-background/50 flex flex-grow flex-col overflow-hidden backdrop-blur-sm">
          {(isLoadingComponents || isLoadingAppData) && (
            <div className="flex flex-grow items-center justify-center">
              <div className="flex animate-pulse flex-col items-center">
                <div className="bg-brand-primary/20 mb-4 h-12 w-12 rounded-full"></div>
                <div className="bg-brand-primary/20 h-4 w-48 rounded"></div>
                <div className="font-secondary text-brand-secondary-font mt-2 text-sm">
                  {isLoadingComponents && isLoadingAppData
                    ? "Loading components and application data..."
                    : isLoadingComponents
                    ? "Loading components..."
                    : "Loading application data..."}
                </div>
                {isLoadingAppData && (
                  <div className="font-secondary text-brand-secondary-font mt-1 text-xs opacity-70">
                    Providers, models, and credentials
                  </div>
                )}
              </div>
            </div>
          )}
          {componentError && (
            <div className="flex flex-grow items-center justify-center">
              <div className="border-brand-unpublish/30 bg-brand-unpublish/10 max-w-md rounded-lg border p-6 text-center">
                <div className="font-primary text-brand-unpublish mb-2 font-medium">
                  Error Loading Components
                </div>
                <div className="font-secondary text-brand-secondary-font text-sm">
                  {componentError}
                </div>
                <button
                  type="button"
                  className="border-brand-unpublish/30 text-brand-unpublish hover:bg-brand-unpublish/10 mt-4 inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium"
                  onClick={() => window.location.reload()}
                >
                  Retry
                </button>
              </div>
            </div>
          )}
          {appDataError && (
            <div className="flex flex-grow items-center justify-center">
              <div className="border-brand-unpublish/30 bg-brand-unpublish/10 max-w-md rounded-lg border p-6 text-center">
                <div className="font-primary text-brand-unpublish mb-2 font-medium">
                  Error Loading Application Data
                </div>
                <div className="font-secondary text-brand-secondary-font text-sm">
                  {appDataError}
                </div>
                <div className="font-secondary text-brand-secondary-font text-xs mt-2 opacity-70">
                  Providers, models, and credentials may not be available
                </div>
                <button
                  type="button"
                  className="border-brand-unpublish/30 text-brand-unpublish hover:bg-brand-unpublish/10 mt-4 inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium"
                  onClick={() => window.location.reload()}
                >
                  Retry
                </button>
              </div>
            </div>
          )}
          {isLoadingWorkflow && (
            <div className="flex flex-grow items-center justify-center">
              <div className="border-brand-stroke bg-brand-card-hover max-w-md rounded-lg border p-6 text-center">
                <Loader2 className="text-brand-primary mx-auto mb-4 h-8 w-8 animate-spin" />
                <div className="font-primary text-brand-primary-font dark:text-brand-white-text mb-2 font-medium">
                  Loading Workflow
                </div>
                <div className="font-secondary text-brand-secondary-font text-sm">
                  Please wait while we load your workflow...
                </div>
              </div>
            </div>
          )}

          {workflowLoadError && (
            <div className="flex flex-grow items-center justify-center">
              <div className="border-brand-unpublish/30 bg-brand-unpublish/10 max-w-md rounded-lg border p-6 text-center">
                <div className="font-primary text-brand-unpublish mb-2 font-medium">
                  Error Loading Workflow
                </div>
                <div className="font-secondary text-brand-secondary-font mb-4 text-sm">
                  {workflowLoadError}
                </div>
                <div className="flex justify-center gap-2">
                  <button
                    type="button"
                    className="border-brand-unpublish/30 text-brand-unpublish hover:bg-brand-unpublish/10 inline-flex h-8 items-center justify-center rounded-md border px-3 py-2 text-sm font-medium"
                    onClick={() => window.location.reload()}
                  >
                    Try Again
                  </button>
                  <Link href="/workflows">
                    <button
                      type="button"
                      className="brand-gradient-indicator text-brand-white-text inline-flex h-8 items-center justify-center rounded-md px-3 py-2 text-sm font-medium"
                    >
                      Back to Workflows
                    </button>
                  </Link>
                </div>
              </div>
            </div>
          )}

          {!isLoadingComponents && !componentError && !isLoadingWorkflow && !workflowLoadError && (
            <WorkflowCanvasWrapper
              onFlowChange={handleFlowChange}
              initialWorkflow={loadedWorkflow || undefined}
              applicationData={{
                providers,
                models,
                credentials,
                isLoadingAppData,
                appDataError
              }}
            />
          )}
        </div>
      </div>

      {/* Execution status overlay with animation */}
      {isExecuting && (
        <div className="brand-gradient-indicator text-brand-white-text animate-in fade-in slide-in-from-bottom-4 absolute bottom-4 left-4 z-50 rounded-lg p-3 text-sm shadow-lg">
          <div className="flex items-center gap-2">
            <div className="h-2 w-2 animate-ping rounded-full bg-white/80"></div>
            <span className="font-primary">Executing workflow...</span>
          </div>
        </div>
      )}

      {/* Result notification */}
      {executionResult && !isExecuting && (
        <div
          className={`animate-in fade-in slide-in-from-bottom-4 absolute right-4 bottom-4 z-50 rounded-lg p-3 text-sm shadow-lg ${executionResult.success ? "bg-brand-tick text-white" : "bg-brand-unpublish text-white"}`}
        >
          <div className="flex items-center gap-2">
            {executionResult.success ? (
              <span className="font-primary">Workflow executed successfully!</span>
            ) : (
              <span className="font-primary">Error: {executionResult.error}</span>
            )}
          </div>
        </div>
      )}

      {/* Load Workflow Modal */}
      <LoadWorkflowModal
        isOpen={isLoadModalOpen}
        onClose={() => setIsLoadModalOpen(false)}
        onLoadWorkflow={handleLoadWorkflow}
      />

      {/* Unsaved Changes Dialog */}
      <UnsavedChangesDialog
        isOpen={isUnsavedChangesDialogOpen}
        onClose={() => setIsUnsavedChangesDialogOpen(false)}
        onContinue={() => {
          setIsUnsavedChangesDialogOpen(false);
          if (pendingAction) {
            pendingAction();
            setPendingAction(null);
          }
        }}
      />
    </main>
  );
}
