/**
 * OAuth utility functions for handling OAuth flow state and callbacks
 */

export interface OAuthCallbackData {
  provider: string;
  toolName: string;
  timestamp: number;
}

/**
 * Store OAuth callback success data in sessionStorage
 */
export function storeOAuthSuccess(data: OAuthCallbackData): void {
  if (typeof window !== "undefined") {
    sessionStorage.setItem("oauth_callback_success", JSON.stringify(data));
  }
}

/**
 * Retrieve and clear OAuth callback success data from sessionStorage
 */
export function getAndClearOAuthSuccess(): OAuthCallbackData | null {
  if (typeof window === "undefined") return null;
  
  try {
    const data = sessionStorage.getItem("oauth_callback_success");
    if (data) {
      sessionStorage.removeItem("oauth_callback_success");
      return JSON.parse(data);
    }
  } catch (error) {
    console.error("Error parsing OAuth success data:", error);
    sessionStorage.removeItem("oauth_callback_success");
  }
  
  return null;
}

/**
 * Check if OAuth success data exists for a specific tool/provider
 */
export function checkOAuthSuccess(toolName?: string, provider?: string): OAuthCallbackData | null {
  if (typeof window === "undefined") return null;
  
  try {
    const data = sessionStorage.getItem("oauth_callback_success");
    if (data) {
      const parsed: OAuthCallbackData = JSON.parse(data);
      
      // Check if this success is for the specified tool/provider
      if (toolName && parsed.toolName === toolName) {
        return parsed;
      }
      if (provider && parsed.provider === provider) {
        return parsed;
      }
      if (!toolName && !provider) {
        return parsed;
      }
    }
  } catch (error) {
    console.error("Error parsing OAuth success data:", error);
    sessionStorage.removeItem("oauth_callback_success");
  }
  
  return null;
}

/**
 * Clear OAuth success data from sessionStorage
 */
export function clearOAuthSuccess(): void {
  if (typeof window !== "undefined") {
    sessionStorage.removeItem("oauth_callback_success");
  }
}

/**
 * Store the current page URL to return to after OAuth
 */
export function storeOAuthReturnUrl(url?: string): void {
  if (typeof window !== "undefined") {
    const returnUrl = url || (window.location.pathname + window.location.search);
    sessionStorage.setItem("oauth_return_url", returnUrl);
  }
}

/**
 * Get and clear the OAuth return URL
 */
export function getAndClearOAuthReturnUrl(): string | null {
  if (typeof window === "undefined") return null;

  const returnUrl = sessionStorage.getItem("oauth_return_url");
  if (returnUrl) {
    sessionStorage.removeItem("oauth_return_url");
  }

  return returnUrl;
}

/**
 * Generate OAuth state parameter for security
 */
export function generateOAuthState(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Store OAuth state for verification
 */
export function storeOAuthState(state: string): void {
  if (typeof window !== "undefined") {
    sessionStorage.setItem("oauth_state", state);
  }
}

/**
 * Retrieve and verify OAuth state
 */
export function verifyOAuthState(receivedState: string): boolean {
  if (typeof window === "undefined") return false;
  
  const storedState = sessionStorage.getItem("oauth_state");
  sessionStorage.removeItem("oauth_state");
  
  return storedState === receivedState;
}

/**
 * Open OAuth URL in a new tab and listen for completion
 */
export function openOAuthInNewTab(
  oauthUrl: string,
  onSuccess?: () => void,
  onError?: (error: string) => void
): void {
  if (typeof window === "undefined") return;

  // Open OAuth URL in new tab
  const oauthTab = window.open(oauthUrl, '_blank', 'width=600,height=700,scrollbars=yes,resizable=yes');

  if (!oauthTab) {
    onError?.("Failed to open OAuth window. Please check your popup blocker settings.");
    return;
  }

  // Listen for messages from the OAuth callback page
  const messageListener = (event: MessageEvent) => {
    // Verify origin for security
    if (event.origin !== window.location.origin) return;

    if (event.data.type === 'OAUTH_SUCCESS') {
      // Close the OAuth tab
      oauthTab.close();

      // Remove the message listener
      window.removeEventListener('message', messageListener);

      // Call success callback
      onSuccess?.();
    } else if (event.data.type === 'OAUTH_ERROR') {
      // Close the OAuth tab
      oauthTab.close();

      // Remove the message listener
      window.removeEventListener('message', messageListener);

      // Call error callback
      onError?.(event.data.error || "OAuth connection failed");
    }
  };

  // Add message listener
  window.addEventListener('message', messageListener);

  // Check if tab was closed manually (fallback)
  const checkClosed = setInterval(() => {
    if (oauthTab.closed) {
      clearInterval(checkClosed);
      window.removeEventListener('message', messageListener);
      // Don't call error callback here as user might have just closed the tab
    }
  }, 1000);
}

/**
 * Hook for React components to listen for OAuth callback success
 */
export function useOAuthCallback(
  toolName?: string,
  provider?: string,
  onSuccess?: (data: OAuthCallbackData) => void
): {
  isListening: boolean;
  checkForSuccess: () => OAuthCallbackData | null;
} {
  const checkForSuccess = (): OAuthCallbackData | null => {
    const successData = checkOAuthSuccess(toolName, provider);
    if (successData && onSuccess) {
      clearOAuthSuccess();
      onSuccess(successData);
    }
    return successData;
  };

  return {
    isListening: true,
    checkForSuccess,
  };
}
