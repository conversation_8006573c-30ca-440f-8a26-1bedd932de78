{"nodes": [{"id": "AgenticAI", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AgenticAI", "input_schema": {"predefined_fields": [{"field_name": "model_provider", "data_type": {"type": "string", "description": "The AI model provider to use."}, "required": false}, {"field_name": "base_url", "data_type": {"type": "string", "description": "Base URL for the API (leave empty for default provider URL)."}, "required": false}, {"field_name": "model_name", "data_type": {"type": "string", "description": "Select the model to use. The list is dynamically fetched from the model provider API when you select a provider."}, "required": false}, {"field_name": "temperature", "data_type": {"type": "number", "description": "Controls randomness: 0 is deterministic, higher values are more random."}, "required": false}, {"field_name": "description", "data_type": {"type": "string", "description": "Description of the agent for UI display."}, "required": false}, {"field_name": "execution_type", "data_type": {"type": "string", "description": "Determines if agent handles single response or multi-turn conversation."}, "required": false}, {"field_name": "query", "data_type": {"type": "string", "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "system_message", "data_type": {"type": "string", "description": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "termination_condition", "data_type": {"type": "string", "description": "Defines when multi-turn conversations should end. Required for interactive execution type."}, "required": false}, {"field_name": "max_tokens", "data_type": {"type": "number", "description": "Maximum response length in tokens."}, "required": false}, {"field_name": "input_variables", "data_type": {"type": "object", "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "memory", "data_type": {"type": "string", "description": "Connect a memory object from another node."}, "required": false}, {"field_name": "autogen_agent_type", "data_type": {"type": "string", "description": "The type of AutoGen agent to create internally."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "final_answer", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "intermediate_steps", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "updated_memory", "data_type": {"type": "string", "description": "", "format": "datetime"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}, {"id": "MergeDataComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "MergeDataComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "object", "description": "The main data structure to merge. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional inputs to show (1-10)."}, "required": false}, {"field_name": "merge_strategy", "data_type": {"type": "string", "description": "How to handle conflicts when merging dictionaries. 'Aggregate' will combine conflicting values into a list. 'Structured Compose' creates custom key-value pairs."}, "required": false}, {"field_name": "output_key_1", "data_type": {"type": "string", "description": "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_2", "data_type": {"type": "string", "description": "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_3", "data_type": {"type": "string", "description": "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_4", "data_type": {"type": "string", "description": "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_5", "data_type": {"type": "string", "description": "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_6", "data_type": {"type": "string", "description": "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_7", "data_type": {"type": "string", "description": "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_8", "data_type": {"type": "string", "description": "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_9", "data_type": {"type": "string", "description": "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_10", "data_type": {"type": "string", "description": "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "output_key_11", "data_type": {"type": "string", "description": "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "object", "description": "Data structure 1 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "object", "description": "Data structure 2 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "object", "description": "Data structure 3 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "object", "description": "Data structure 4 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "object", "description": "Data structure 5 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "object", "description": "Data structure 6 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "object", "description": "Data structure 7 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "object", "description": "Data structure 8 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "object", "description": "Data structure 9 to merge. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "object", "description": "Data structure 10 to merge. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_data", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}], "transitions": [{"id": "transition-MergeDataComponent-1751626170252", "sequence": 1, "transition_type": "initial", "execution_type": "Components", "node_info": {"node_id": "MergeDataComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "MergeDataComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "object", "field_value": null}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": null}, {"field_name": "merge_strategy", "data_type": "string", "field_value": null}, {"field_name": "output_key_1", "data_type": "string", "field_value": null}, {"field_name": "output_key_2", "data_type": "string", "field_value": null}, {"field_name": "output_key_3", "data_type": "string", "field_value": null}, {"field_name": "output_key_4", "data_type": "string", "field_value": null}, {"field_name": "output_key_5", "data_type": "string", "field_value": null}, {"field_name": "output_key_6", "data_type": "string", "field_value": null}, {"field_name": "output_key_7", "data_type": "string", "field_value": null}, {"field_name": "output_key_8", "data_type": "string", "field_value": null}, {"field_name": "output_key_9", "data_type": "string", "field_value": null}, {"field_name": "output_key_10", "data_type": "string", "field_value": null}, {"field_name": "output_key_11", "data_type": "string", "field_value": null}, {"field_name": "input_1", "data_type": "object", "field_value": null}, {"field_name": "input_2", "data_type": "object", "field_value": null}, {"field_name": "input_3", "data_type": "object", "field_value": null}, {"field_name": "input_4", "data_type": "object", "field_value": null}, {"field_name": "input_5", "data_type": "object", "field_value": null}, {"field_name": "input_6", "data_type": "object", "field_value": null}, {"field_name": "input_7", "data_type": "object", "field_value": null}, {"field_name": "input_8", "data_type": "object", "field_value": null}, {"field_name": "input_9", "data_type": "object", "field_value": null}, {"field_name": "input_10", "data_type": "object", "field_value": null}]}}], "input_data": [], "output_data": [{"to_transition_id": "transition-AgenticAI-1751521933685", "target_node_id": "AI Agent Executor", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "output_data", "result_path": "output_data", "edge_id": "reactflow__edge-MergeDataComponent-1751626170252output_data-AgenticAI-1751521933685input_variables"}]}}]}, "result_resolution": {"node_type": "component", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "main_input", "handle_name": "Main Input", "data_type": "object", "required": true, "description": "The main data structure to merge. Can be connected from another node or entered directly."}, {"handle_id": "output_key_1", "handle_name": "Output Key 1", "data_type": "string", "required": false, "description": "Custom key name for input 1 (e.g., 'data_1'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_2", "handle_name": "Output Key 2", "data_type": "string", "required": false, "description": "Custom key name for input 2 (e.g., 'data_2'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_3", "handle_name": "Output Key 3", "data_type": "string", "required": false, "description": "Custom key name for input 3 (e.g., 'data_3'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_4", "handle_name": "Output Key 4", "data_type": "string", "required": false, "description": "Custom key name for input 4 (e.g., 'data_4'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_5", "handle_name": "Output Key 5", "data_type": "string", "required": false, "description": "Custom key name for input 5 (e.g., 'data_5'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_6", "handle_name": "Output Key 6", "data_type": "string", "required": false, "description": "Custom key name for input 6 (e.g., 'data_6'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_7", "handle_name": "Output Key 7", "data_type": "string", "required": false, "description": "Custom key name for input 7 (e.g., 'data_7'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_8", "handle_name": "Output Key 8", "data_type": "string", "required": false, "description": "Custom key name for input 8 (e.g., 'data_8'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_9", "handle_name": "Output Key 9", "data_type": "string", "required": false, "description": "Custom key name for input 9 (e.g., 'data_9'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_10", "handle_name": "Output Key 10", "data_type": "string", "required": false, "description": "Custom key name for input 10 (e.g., 'data_10'). Only used with Structured Compose strategy."}, {"handle_id": "output_key_11", "handle_name": "Output Key 11", "data_type": "string", "required": false, "description": "Custom key name for input 11 (e.g., 'data_11'). Only used with Structured Compose strategy."}, {"handle_id": "input_1", "handle_name": "Input 1", "data_type": "object", "required": false, "description": "Data structure 1 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_2", "handle_name": "Input 2", "data_type": "object", "required": false, "description": "Data structure 2 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_3", "handle_name": "Input 3", "data_type": "object", "required": false, "description": "Data structure 3 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_4", "handle_name": "Input 4", "data_type": "object", "required": false, "description": "Data structure 4 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_5", "handle_name": "Input 5", "data_type": "object", "required": false, "description": "Data structure 5 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_6", "handle_name": "Input 6", "data_type": "object", "required": false, "description": "Data structure 6 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_7", "handle_name": "Input 7", "data_type": "object", "required": false, "description": "Data structure 7 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_8", "handle_name": "Input 8", "data_type": "object", "required": false, "description": "Data structure 8 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_9", "handle_name": "Input 9", "data_type": "object", "required": false, "description": "Data structure 9 to merge. Can be connected from another node or entered directly."}, {"handle_id": "input_10", "handle_name": "Input 10", "data_type": "object", "required": false, "description": "Data structure 10 to merge. Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "output_data", "handle_name": "Merged Data", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"output_data": "output_data", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.output_data", "output_data.output_data", "response.output_data", "data.output_data", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "output_data"}}, "approval_required": false, "end": false}, {"id": "transition-AgenticAI-1751521933685", "sequence": 2, "transition_type": "standard", "execution_type": "agent", "node_info": {"node_id": "AgenticAI", "tools_to_use": [{"tool_id": 1, "tool_name": "AgenticAI", "tool_params": {"items": [{"field_name": "agent_type", "data_type": "string", "field_value": "component"}, {"field_name": "execution_type", "data_type": "string", "field_value": "response"}, {"field_name": "query", "data_type": "string", "field_value": "Research investments 2024 2025 portfolio companies for given Investor and VC Firm."}, {"field_name": "input", "data_type": "string", "field_value": ""}, {"field_name": "system_message", "data_type": "string", "field_value": "You are a research assistant specializing in venture capital investments. Provide detailed, factual information about investor activities and portfolio companies.\n\nInvestor Name - ${{investor_name}} \nVC Firm - ${{vc_firm}} \n\n\nAlways return response in following format - \n\n{\n    \"result\": {\n             \"data\" : \"response of LLM here.... as string\"\n     },\n     \"investor_name\" : \"investor name which is given as input parameter\",\n     \"vc_firm\": \"vc firm name is given as input parameter\"\n\n}"}, {"field_name": "input_variables", "data_type": "object", "field_value": null}, {"field_name": "agent_config", "data_type": "object", "field_value": {"model_config": {"model_provider": "openai", "model": "gpt-4o-mini", "temperature": 0.7, "max_tokens": 1000}, "description": "response generator", "autogen_agent_type": "Assistant", "termination_condition": ""}}, {"field_name": "variables", "data_type": "object", "field_value": {"{investor_name": "${{investor_name}", "{vc_firm": "${{vc_firm}"}}]}}], "input_data": [{"from_transition_id": "transition-MergeDataComponent-1751626170252", "source_node_id": "Merge Data", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-MergeDataComponent-1751626170252", "source_handle_id": "output_data", "target_handle_id": "input_variables", "edge_id": "reactflow__edge-MergeDataComponent-1751626170252output_data-AgenticAI-1751521933685input_variables"}]}], "output_data": []}, "result_resolution": {"node_type": "agent", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "query", "handle_name": "Query/Objective", "data_type": "string", "required": true, "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, {"handle_id": "system_message", "handle_name": "System Message", "data_type": "string", "required": false, "description": "System prompt/instructions for the agent. If empty, will use default based on query. Can be connected from another node or entered directly."}, {"handle_id": "input_variables", "handle_name": "Input Variables", "data_type": "object", "required": false, "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, {"handle_id": "tools", "handle_name": "Tools", "data_type": "string", "required": false, "description": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle."}, {"handle_id": "memory", "handle_name": "Memory Object", "data_type": "string", "required": false, "description": "Connect a memory object from another node."}], "output_handles": [{"handle_id": "final_answer", "handle_name": "Final Answer", "data_type": "string", "description": ""}, {"handle_id": "intermediate_steps", "handle_name": "Intermediate Steps", "data_type": "string", "description": ""}, {"handle_id": "updated_memory", "handle_name": "Updated Memory", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"final_answer": "final_answer", "intermediate_steps": "intermediate_steps", "updated_memory": "updated_memory", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.final_answer", "output_data.final_answer", "response.final_answer", "data.final_answer", "result.intermediate_steps", "output_data.intermediate_steps", "response.intermediate_steps", "data.intermediate_steps", "result.updated_memory", "output_data.updated_memory", "response.updated_memory", "data.updated_memory", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "final_answer"}}, "approval_required": false, "end": true}]}